# Excel Extractor for Chess Brigade API

This tool extracts data from Excel files and inserts it into the database. It's designed to handle various table structures and automatically detect table boundaries.

## Features

- Automatically detects tables in Excel files
- Maps extracted data to the ReportExtract model
- Handles blank columns and columns with data but no header
- Associates extracted data with tournaments
- Configurable field mapping

## Usage

### Extract Data and Save to Database

```bash
node scripts/excel-extractor.js [file.xlsx] [tournamentId]
```

- `file.xlsx`: Path to the Excel file to process
- `tournamentId` (optional): UUID of the tournament to associate the data with

### Test Extraction Without Saving to Database

```bash
node scripts/test-extractor.js [file.xlsx]
```

This will extract the data and save it to a JSON file for inspection without affecting the database.

## Data Mapping

The extractor maps common field names to the ReportExtract model fields:

| Excel Header | Model Field |
|--------------|-------------|
| No., Number, Player No | playerNumber |
| Name, Player Name | playerName |
| ID, Player ID | playerId |
| FideID, FIDE ID | fideId |
| Rtg, Rating | rating |
| sex, Gender | gender |
| Gr, Category, Group, Typ | category |
| Club | club |
| Club/City | city |

Any fields that don't match these mappings are stored in the `additionalData` JSON field.

## Customizing Field Mapping

To customize the field mapping, edit the `fieldMapping` object in the `mapDataToModel` function in `excel-extractor.js`.

## Database Model

The extracted data is stored in the `report_extract` table with the following fields:

- `id`: UUID (primary key)
- `tournamentId`: UUID (foreign key to tournament table)
- `playerNumber`: Integer
- `playerName`: String
- `playerId`: String
- `fideId`: String
- `rating`: Integer
- `gender`: String
- `category`: String
- `club`: String
- `city`: String
- `sourceFile`: String
- `importDate`: Date
- `additionalData`: JSON

## Error Handling

The extractor includes error handling for:

- File not found
- No table detected in the worksheet
- Database insertion errors

Error messages are logged to the console.
