const express = require("express");
const dotenv = require("dotenv");
const cookieParser = require("cookie-parser");
const helmet = require("helmet");
const router = require("./routes/index");
const { connectDb } = require("./config/db");
const cors = require("cors");
const cronService = require("./services/cronService");
const { rateLimit } = require("express-rate-limit");

dotenv.config();

const app = express();

const allowedOrigins = [
  "http://************:5173",
  "http://**************:5173",
  "http://**************:3000",
  "http://**********:5173",
  "http://**********:3000",
  "http://localhost:5173",
  "http://localhost:3000",
];

// const limiter = rateLimit({
//   windowMs: 1 * 60 * 1000,
//   max: 15,
//   message: "Rate limit exceeded, please slow down.",
//   standardHeaders: true,
//   legacyHeaders: false,
// });

// Development mode
// if (process.env.NODE_ENV === "development") {
//   app.use(
//     helmet({
//       contentSecurityPolicy: false,
//       crossOriginEmbedderPolicy: false,
//     })
//   );
// } else {
//   app.use(
//     helmet({
//       // Content Security Policy - important for file uploads
//       contentSecurityPolicy: {
//         directives: {
//           defaultSrc: ["'self'"],
//           imgSrc: [
//             "'self'",
//             "data:",
//             "https://your-s3-bucket.s3.amazonaws.com",
//           ], // Allow S3 images
//           connectSrc: ["'self'", "https://your-api-domain.com"],
//           uploadSrc: ["'self'"], // For file uploads
//           objectSrc: ["'none'"],
//           scriptSrc: ["'self'"],
//           styleSrc: ["'self'", "'unsafe-inline'"],
//         },
//       },

//       // Cross-Origin settings for API
//       crossOriginEmbedderPolicy: false, // Often needed for file uploads
//       crossOriginResourcePolicy: { policy: "cross-origin" }, // Allow cross-origin requests

//       // Security headers
//       hsts: {
//         maxAge: 31536000, // 1 year
//         includeSubDomains: true,
//         preload: true,
//       },

//       // Referrer policy
//       referrerPolicy: { policy: "same-origin" },

//       // Don't reveal server info
//       hidePoweredBy: true,

//       // Frame options
//       frameguard: { action: "deny" },

//       // MIME type sniffing protection
//       noSniff: true,

//       // XSS protection
//       xssFilter: true,
//     })
//   );
// }
// app.use(limiter);
// app.use(
//   cors({
//     origin: (origin, callback) => {
//       if (!origin || allowedOrigins.includes(origin)) {
//         callback(null, true);
//       } else {
//         callback(new Error("Not allowed by CORS"));
//       }
//     },
//     methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
//     allowedHeaders: ["Content-Type", "Authorization"],
//     exposedHeaders: ["Authorization"],
//     credentials: true,
//   })
// );
app.use(
  cors({
    origin: true,
    credentials: true,
  })
);
app.options("*", cors());
app.use(cookieParser());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Connect to database
connectDb();

// Routes
app.use("/api/v1", router);

app.get("/health", (req, res) => {
  res.status(200).json({ status: "OK" });
});

const PORT = process.env.PORT || 3000;
// app.listen(PORT, () => {
//   console.log(`Server is running on port ${PORT}`);
// });
app.listen(3000, "0.0.0.0", () => {
  console.log(`Server is running on port ${PORT}`);

  // // Initialize cron jobs
  // cronService.initCronJobs();
  // console.log('Cron jobs initialized');
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: "Internal Server Error" });
});

// Handle unhandled promise rejections
process.on("unhandledRejection", (reason, promise) => {
  console.error("Unhandled Rejection at:", promise, "reason:", reason);
  // Application specific logging, throwing an error, or other logic here
});
