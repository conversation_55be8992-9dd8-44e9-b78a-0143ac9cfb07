"use strict";

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("rankings", {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      tournament_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: "tournament",
          key: "id",
        },
        onDelete: "CASCADE",
      },
      round_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      age_category: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      gender_category: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      player_name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      fide_id: {
        type: Sequelize.STRING(20),
        allowNull: true,
      },
      fide_rating: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      association: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      total_points: {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: false,
        defaultValue: 0.0,
      },
      rank: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      tie_breakers: {
        type: Sequelize.JSONB, // Change to Sequelize.JSON if using MySQL
        allowNull: true,
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal("NOW()"),
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal("NOW()"),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("rankings");
  },
};
