const compare = (
  a,
  b,
  // eslint-disable-next-line no-unused-vars
  keyToCompare = defaultKeyToCompare
) => {
  if (keyToCompare(a) < keyToCompare(b)) return -1;
  if (keyToCompare(a) > keyToCompare(b)) return 1;
  return 0;
};
const defaultKeyToCompare = (entity) => {
  return entity.name;
};

const convertArrayToObject = (keys, arr) => {
  const result = arr.map((subArr) => {
    return Object.fromEntries(keys.map((key, index) => [key, subArr[index]]));
  });
  return result;
};

module.exports = {
  compare,
  convertArrayToObject,
};
