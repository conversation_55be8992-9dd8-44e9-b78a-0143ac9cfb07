function CheckEligibleCategory(
  tournamentCategory, // 'open' | 'male' | 'female' | 'children' | 'children-open'
  dob,
  gender,
  userAgeCategory,
  userGenderCategory
) {
  const birthDate = new Date(dob);
  const today = new Date();

  let age = today.getFullYear() - birthDate.getFullYear();
  if (
    today.getMonth() < birthDate.getMonth() ||
    (today.getMonth() === birthDate.getMonth() && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }

  // Optional age category check (e.g., "U18")
  const userAgeLimit = userAgeCategory?.toUpperCase()?.startsWith("U")
    ? parseInt(userAgeCategory.substring(1))
    : null;

  // Apply age limit only for categories that have it
  // Not applying age limit for 'children' or 'children-open'
  if (
    userAgeLimit !== null &&
    !["children", "children-open"].includes(tournamentCategory) &&
    age >= userAgeLimit
  ) {
    return {
      eligible: false,
      reason: `Player age (${age}) exceeds or equals selected age category (${userAgeCategory})`,
    };
  }

  // Tournament & gender validation
  if (tournamentCategory === "female") {
    if (gender !== "female" || userGenderCategory !== "female") {
      return {
        eligible: false,
        reason: "Only female players can participate in the 'female' tournament category",
      };
    }
  } else if (tournamentCategory === "male") {
    if (userGenderCategory !== "male") {
      return {
        eligible: false,
        reason: "In male tournaments, players must select the 'male' category",
      };
    }
  } else if (tournamentCategory === "open") {
    if (gender === "male" && userGenderCategory !== "male") {
      return {
        eligible: false,
        reason: "Male players in open tournaments can only select 'male' category",
      };
    }
    if (gender === "female" && !["male", "female"].includes(userGenderCategory)) {
      return {
        eligible: false,
        reason: "Female players in open tournaments can select 'male' or 'female' category only",
      };
    }
  } else if (tournamentCategory === "children") {
    // ✅ No age restriction, no gender restriction
  } else if (tournamentCategory === "children-open") {
    // ✅ No age restriction, no gender restriction
  } else {
    return {
      eligible: false,
      reason: "Invalid tournament category",
    };
  }

  return {
    eligible: true,
    reason: "Player is eligible",
  };
}

module.exports = { CheckEligibleCategory };
