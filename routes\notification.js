const express = require("express");
const router = express.Router();
const notificationController = require("../controllers/notificationController");
const verifyJwt = require("../middlewares/verifyJwt");

/**
 * @route POST /api/v1/notification/pairing
 * @desc Create pairing notifications for a tournament round
 * @access Private (requires authentication)
 */
router.post(
  "/pairing",
  verifyJwt,
  notificationController.createPairingNotifications
);

/**
 * @route POST /api/v1/notification/process-sms
 * @desc Process pending SMS notifications immediately
 * @access Private (requires authentication)
 */
router.post(
  "/process-sms",
  verifyJwt,
  notificationController.processSmsNotifications
);

/**
 * @route GET /api/v1/notification/status
 * @desc Get notification status
 * @access Private (requires authentication)
 */
router.get(
  "/status",
  verifyJwt,
  notificationController.getNotificationStatus
);

/**
 * @route POST /api/v1/notification/bulk-registration
 * @desc Create bulk registration notifications
 * @access Private (requires authentication)
 */
router.post(
  "/bulk-registration",
  verifyJwt,
  notificationController.createBulkRegistrationNotifications
);

module.exports = router;
