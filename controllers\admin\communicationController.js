const { sendResponse, handleError } = require("../../utils/apiResponse");
const smsService = require("../../utils/sms/smsService");
const { z } = require("zod");
const { v4: uuidv4 } = require("uuid");
const cronService = require("../../services/cronService");
const { Notifications } = require("../../config/db").models;
const { Op } = require("sequelize");
const { getRecipients } = require("./userController");
const { config } = require("../../config/config");
// Email templates removed - Admin can send any custom content directly

/**
 * Get all available SMS templates
 */
const getSmsTemplates = async (req, res) => {
  try {
    // SMS templates are predefined in the service
    const templates = [
      {
        id: "welcome",
        name: "Welcome SMS",
        category: "user",
        description: "Welcome message for new users",
        variables: ["VAR1", "VAR2"], // VAR1: name, VAR2: frontend_url
        content:
          "Welcome {{VAR1}} to Chess Brigade! Verify your account by clicking the link: {{VAR2}}",
        templateId: config.msg91.templates.otp_template_id || "",
        maxLength: 160,
      },
    ];

    // Group templates by category
    const categorizedTemplates = templates.reduce((acc, template) => {
      if (!acc[template.category]) {
        acc[template.category] = [];
      }
      acc[template.category].push(template);
      return acc;
    }, {});

    sendResponse(res, 200, {
      success: true,
      data: {
        templates: categorizedTemplates,
        totalTemplates: templates.length,
      },
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * Get all available WhatsApp templates
 */
const getWhatsappTemplates = async (req, res) => {
  try {
    // WhatsApp templates (similar to SMS for now)
    const templates = [
      {
        id: "welcome",
        name: "Welcome Message",
        category: "user",
        description: "Welcome message for new users",
        variables: ["name", "platform_url"],
        maxLength: 4096,
      },
      {
        id: "tournament-registration",
        name: "Tournament Registration",
        category: "tournament",
        description: "Tournament registration confirmation",
        variables: ["playerName", "tournamentName", "startDate", "venue"],
        maxLength: 4096,
      },
      {
        id: "payment-confirmation",
        name: "Payment Confirmation",
        category: "payment",
        description: "Payment confirmation message",
        variables: ["playerName", "amount", "transactionId", "tournamentName"],
        maxLength: 4096,
      },
      {
        id: "club-invitation",
        name: "Club Invitation",
        category: "club",
        description: "Club invitation message",
        variables: ["playerName", "clubName", "inviterName"],
        maxLength: 4096,
      },
    ];

    // Group templates by category
    const categorizedTemplates = templates.reduce((acc, template) => {
      if (!acc[template.category]) {
        acc[template.category] = [];
      }
      acc[template.category].push(template);
      return acc;
    }, {});

    sendResponse(res, 200, {
      success: true,
      data: {
        templates: categorizedTemplates,
        totalTemplates: templates.length,
      },
    });
  } catch (error) {
    handleError(res, error);
  }
};

const schema = z.object({
  recipients: z
    .array(
      z.object({
        email: z.string().email(),
        name: z.string().optional(),
        id: z.string().optional(),
        type: z.enum(["player", "club", "arbiter"]).optional(),
      })
    )
    .optional(),
  subject: z.string().min(1, "Subject is required"),
  content: z.string().optional(),
  recipientType: z.enum(["player", "club", "arbiter"]).optional(),
  bulkSend: z.boolean().optional(),
  searchFilters: z
    .object({
      playerName: z.string().optional(),
      playerId: z.string().optional(),
      clubName: z.string().optional(),
      arbiterName: z.string().optional(),
      arbiterId: z.string().optional(),
      email: z.string().email().optional(),
      country: z.string().optional(),
      state: z.string().optional(),
      district: z.string().optional(),
      city: z.string().optional(),
    })
    .optional(),
});

// Helper functions removed - No longer needed for custom content approach

/**
 * Send bulk email - Creates notifications in database for reliable delivery
 */
const sendSelectiveBulkEmail = async (req, res) => {

  try {
    const { data, success, error } = schema.safeParse(req.body);
    if (!success) {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid request data",
        details: error.errors,
      });
    }

    const { recipients, subject, content } = data;

    // Generate batch ID for tracking
    const batchId = uuidv4();
    const creatorId = req.user.userId;
    const now = new Date();
    const expiresAt = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    const priority = 3;

    // Create notifications for each recipient
    const notifications = [];

    for (const recipient of recipients) {
      // Prepare email content with Chess Brigade branding
      const emailContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: #2c5aa0; color: white; padding: 20px; text-align: center;">
            <h1>Chess Brigade</h1>
            <p style="margin: 0; color: #ffffff;">Connecting Chess Players Worldwide</p>
          </div>
          <div style="padding: 20px;">
            <h2>Hello ${recipient.name || "Chess Enthusiast"},</h2>
            ${content}
          </div>
          <div style="background: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666;">
            <p style="margin: 5px 0;">
              <strong>Chess Brigade</strong><br>
              Email: <EMAIL> | Website: www.chessbrigade.com
            </p>
            <p style="margin: 5px 0;">
              © ${new Date().getFullYear()} Chess Brigade. All rights reserved.
            </p>
          </div>
        </div>
      `;

      const notification = {
        userId: recipient.id || null,
        creatorId,
        email: recipient.email,
        type: "promotional",
        platform: "email",
        templateId: "custom",
        content: {
          subject,
          customContent: emailContent,
          recipientName: recipient.name || "User",
        },
        status: "pending",
        priority,
        expiresAt,
        batchId,
        metadata: {
          bulkEmail: true,
          batchId,
          createdBy: creatorId,
          originalSubject: subject,
          hasCustomContent: true,
          contentLength: content.length,
        },
      };

      notifications.push(notification);
    }

    // Save notifications in batches
    const chunkSize = 100;
    let createdNotifications = [];

    for (let i = 0; i < notifications.length; i += chunkSize) {
      const chunk = notifications.slice(i, i + chunkSize);
      const created = await Notifications.bulkCreate(chunk);
      createdNotifications = createdNotifications.concat(created);
    }
    cronService.runJobNow("process-promotional-email-notifications");

    sendResponse(res, 200, {
      success: true,
      data: {
        batchId,
        totalRecipients: recipients.length,
        notificationsCreated: createdNotifications.length,
        status: "queued",
        message:
          "Custom emails have been queued for delivery. They will be processed by the notification service.",
        estimatedDeliveryTime: "5-15 minutes",
        expiresAt,
        contentPreview:
          content.substring(0, 100) + (content.length > 100 ? "..." : ""),
      },
    });
  } catch (error) {
    handleError(res, error);
  }
};
const sendBulkEmail = async (req, res) => {

  try {
    const { data, success, error } = schema.safeParse(req.body);
    if (!success) {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid request data",
        details: error.errors,
      });
    }

    const { recipientType, searchFilters, bulkSend, subject, content } = data;

    // Generate batch ID for tracking
    const batchId = uuidv4();
    const creatorId = req.user.userId;
    const now = new Date();
    const expiresAt = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    const priority = 3;

    const recipients = await getRecipients(
      recipientType,
      searchFilters,
      bulkSend
    );

    // Create notifications for each recipient
    const notifications = [];

    for (const recipient of recipients) {
      // Prepare email content with Chess Brigade branding
      const emailContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: #2c5aa0; color: white; padding: 20px; text-align: center;">
            <h1>Chess Brigade</h1>
            <p style="margin: 0; color: #ffffff;">Connecting Chess Players Worldwide</p>
          </div>
          <div style="padding: 20px;">
            <h2>Hello ${recipient.name || "Chess Enthusiast"},</h2>
            ${content}
          </div>
          <div style="background: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666;">
            <p style="margin: 5px 0;">
              <strong>Chess Brigade</strong><br>
              Email: <EMAIL> | Website: www.chessbrigade.com
            </p>
            <p style="margin: 5px 0;">
              © ${new Date().getFullYear()} Chess Brigade. All rights reserved.
            </p>
          </div>
        </div>
      `;

      const notification = {
        userId: recipient.id || null,
        creatorId,
        email: recipient.email,
        type: "promotional",
        platform: "email",
        templateId: "custom",
        content: {
          subject,
          customContent: emailContent,
          recipientName: recipient.name || "User",
        },
        status: "pending",
        priority,
        expiresAt,
        batchId,
        metadata: {
          bulkEmail: true,
          batchId,
          createdBy: creatorId,
          originalSubject: subject,
          hasCustomContent: true,
          contentLength: content.length,
        },
      };

      notifications.push(notification);
    }

    // Save notifications in batches
    const chunkSize = 100;
    let createdNotifications = [];

    for (let i = 0; i < notifications.length; i += chunkSize) {
      const chunk = notifications.slice(i, i + chunkSize);
      const created = await Notifications.bulkCreate(chunk);
      createdNotifications = createdNotifications.concat(created);
    }
    cronService.runJobNow("process-promotional-email-notifications");

    sendResponse(res, 200, {
      success: true,
      data: {
        batchId,
        totalRecipients: recipients.length,
        notificationsCreated: createdNotifications.length,
        status: "queued",
        message:
          "Custom emails have been queued for delivery. They will be processed by the notification service.",
        estimatedDeliveryTime: "5-15 minutes",
        expiresAt,
        contentPreview:
          content.substring(0, 100) + (content.length > 100 ? "..." : ""),
      },
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * Send bulk SMS - Creates notifications in database for reliable delivery
 */
const sendSelectiveBulkSms = async (req, res) => {
  try {
    const schema = z.object({
      recipients: z
        .array(
          z.object({
            phone: z.string().min(10, "Valid mobile number is required"),
            name: z.string().optional(),
            id: z.string().optional(),
            type: z.enum(["player", "club", "arbiter"]).optional(),
          })
        )
        .min(1, "At least one recipient is required"),
      templateId: z.string().min(1, "Template ID is required"),
      templateVariables: z.record(z.any()).optional(),
      recipientType: z.string().optional(),
      content: z.string().optional(),
      priority: z.number().int().min(1).max(5).default(2),
      expiresInHours: z.number().int().min(1).max(168).default(24),
    });

    const { data, success, error } = schema.safeParse(req.body);
    if (!success) {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid request data",
        details: error.errors,
      });
    }

    const {
      recipients,
      templateId,
      templateVariables,
      priority,
      expiresInHours,
    } = data;

    // Ensure templateVariables is properly handled
    const variables =
      templateVariables && typeof templateVariables === "object"
        ? { ...templateVariables }
        : {};



    const { Notifications } = require("../../config/db").models;

    // Generate batch ID for tracking
    const batchId = uuidv4();
    const creatorId = req.user.userId;
    const now = new Date();
    const expiresAt = new Date(now.getTime() + expiresInHours * 60 * 60 * 1000);

    // Create notifications for each recipient
    const notifications = [];

    for (const recipient of recipients) {
      // Create a clean content object for each recipient
      const contentData = {
        ...variables,
      };

      

      const notification = {
        userId: recipient.id || null,
        creatorId,
        phoneNumber: recipient.phone,
        type: "promotional",
        platform: "sms",
        templateId,
        content: contentData, // Store the complete content object
        status: "pending",
        priority,
        expiresAt,
        batchId,
        metadata: {
          bulkSms: true,
          batchId,
          createdBy: creatorId,
          templateUsed: templateId,
          recipientType: data.recipientType || "unknown",
        },
      };

      notifications.push(notification);
    }



    // Save notifications in batches
    const chunkSize = 100;
    let createdNotifications = [];

    for (let i = 0; i < notifications.length; i += chunkSize) {
      const chunk = notifications.slice(i, i + chunkSize);
      try {
        const created = await Notifications.bulkCreate(chunk);
        createdNotifications = createdNotifications.concat(created);
      
      } catch (dbError) {
        console.error("Database error during bulk create:", dbError);
        throw dbError;
      }
    }

    // Log the first created notification to verify content
    if (createdNotifications.length > 0) {
      const firstNotification = await Notifications.findByPk(
        createdNotifications[0].id
      );
   
    }

    sendResponse(res, 200, {
      success: true,
      data: {
        batchId,
        totalRecipients: recipients.length,
        notificationsCreated: createdNotifications.length,
        status: "queued",
        message:
          "SMS messages have been queued for delivery. They will be processed by the notification service.",
        estimatedDeliveryTime: "5-15 minutes",
        expiresAt,
        templateVariables: variables, // Include in response for verification
      },
    });
  } catch (error) {
    console.error("Error in sendBulkSms:", error);
    handleError(res, error);
  }
};
const sendBulkSms = async (req, res) => {
  try {
    const schema = z.object({
     
      templateId: z.string().min(1, "Template ID is required"),
      templateVariables: z.record(z.any()).optional(),
      recipientType: z.enum(["player", "club", "arbiter"]).optional(),
      content: z.string().optional(),
      bulkSend: z.boolean().optional(),
      searchFilters: z
        .object({
          playerName: z.string().optional(),
          playerId: z.string().optional(),
          clubName: z.string().optional(),
          arbiterName: z.string().optional(),
          arbiterId: z.string().optional(),
          email: z.string().email().optional(),
          country: z.string().optional(),
          state: z.string().optional(),
          district: z.string().optional(),
          city: z.string().optional(),
        })
        .optional(),
    });

    const { data, success, error } = schema.safeParse(req.body);
    if (!success) {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid request data",
        details: error.errors,
      });
    }

    const {
      recipientType,
      searchFilters,
      bulkSend,
      templateId,
      templateVariables,
    } = data;

    // Ensure templateVariables is properly handled
    const variables =
      templateVariables && typeof templateVariables === "object"
        ? { ...templateVariables }
        : {};



    const { Notifications } = require("../../config/db").models;

    // Generate batch ID for tracking
    const batchId = uuidv4();
    const creatorId = req.user.userId;
    const now = new Date();
    const expiresAt = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    const priority = 3;

    const recipients = await getRecipients(
      recipientType,
      searchFilters,
      bulkSend
    );

    // Create notifications for each recipient
    const notifications = [];

    for (const recipient of recipients) {
      // Create a clean content object for each recipient
      const contentData = {
        ...variables,
      };

    

      const notification = {
        userId: recipient.id || null,
        creatorId,
        phoneNumber:recipient.phoneNumber,
        type: "promotional",
        platform: "sms",
        templateId,
        content: contentData, // Store the complete content object
        status: "pending",
        priority,
        expiresAt,
        batchId,
        metadata: {
          bulkSms: true,
          batchId,
          createdBy: creatorId,
          templateUsed: templateId,
          recipientType: data.recipientType || "unknown",
        },
      };

      notifications.push(notification);
    }



    // Save notifications in batches
    const chunkSize = 100;
    let createdNotifications = [];

    for (let i = 0; i < notifications.length; i += chunkSize) {
      const chunk = notifications.slice(i, i + chunkSize);
      try {
        const created = await Notifications.bulkCreate(chunk);
        createdNotifications = createdNotifications.concat(created);
      
      } catch (dbError) {
        console.error("Database error during bulk create:", dbError);
        throw dbError;
      }
    }

    // Log the first created notification to verify content
    if (createdNotifications.length > 0) {
      const firstNotification = await Notifications.findByPk(
        createdNotifications[0].id
      );
  
    }

    sendResponse(res, 200, {
      success: true,
      data: {
        batchId,
        totalRecipients: recipients.length,
        notificationsCreated: createdNotifications.length,
        status: "queued",
        message:
          "SMS messages have been queued for delivery. They will be processed by the notification service.",
        estimatedDeliveryTime: "5-15 minutes",
        expiresAt,
        templateVariables: variables, // Include in response for verification
      },
    });
  } catch (error) {
    console.error("Error in sendBulkSms:", error);
    handleError(res, error);
  }
};

const sendBulkWhatsapp = async (req, res) => {
  try {
    const schema = z.object({
      recipients: z
        .array(
          z.object({
            mobile: z.string().min(10, "Valid mobile number is required"),
            name: z.string().optional(),
            id: z.string().optional(),
          })
        )
        .min(1, "At least one recipient is required"),
      templateId: z.string().min(1, "Template ID is required"),
      variables: z.object({}).optional(),
      priority: z.number().int().min(1).max(5).default(2),
      expiresInHours: z.number().int().min(1).max(168).default(24),
    });

    const { data, success, error } = schema.safeParse(req.body);
    if (!success) {
      return sendResponse(res, 400, {
        success: false,
        error: "Invalid request data",
        details: error.errors,
      });
    }

    const {
      recipients,
      templateId,
      variables = {},
      priority,
      expiresInHours,
    } = data;

    const { Notifications } = require("../../config/db").models;

    // Generate batch ID for tracking
    const batchId = uuidv4();
    const creatorId = req.user.userId;
    const now = new Date();
    const expiresAt = new Date(now.getTime() + expiresInHours * 60 * 60 * 1000);

    // Create notifications for each recipient
    const notifications = [];

    for (const recipient of recipients) {
      const notification = {
        userId: recipient.id || null,
        creatorId,
        phoneNumber: recipient.mobile,
        type: "promotional",
        platform: "whatsapp",
        templateId,
        content: {
          ...variables,
          recipientName: recipient.name || "User",
        },
        status: "pending",
        priority,
        expiresAt,
        batchId,
        metadata: {
          bulkWhatsapp: true,
          batchId,
          createdBy: creatorId,
          templateUsed: templateId,
        },
      };

      notifications.push(notification);
    }

    // Save notifications in batches
    const chunkSize = 100;
    let createdNotifications = [];

    for (let i = 0; i < notifications.length; i += chunkSize) {
      const chunk = notifications.slice(i, i + chunkSize);
      const created = await Notifications.bulkCreate(chunk);
      createdNotifications = createdNotifications.concat(created);
    }

    sendResponse(res, 200, {
      success: true,
      data: {
        batchId,
        totalRecipients: recipients.length,
        notificationsCreated: createdNotifications.length,
        status: "queued",
        message:
          "WhatsApp messages have been queued for delivery. They will be processed by the notification service.",
        estimatedDeliveryTime: "5-15 minutes",
        expiresAt,
        note: "WhatsApp integration is currently in development.",
      },
    });
  } catch (error) {
    handleError(res, error);
  }
};

module.exports = {
  getSmsTemplates,
  getWhatsappTemplates,
  sendBulkEmail,
  sendSelectiveBulkEmail,
  sendBulkSms,
  sendBulkWhatsapp,
  sendSelectiveBulkSms,
};
