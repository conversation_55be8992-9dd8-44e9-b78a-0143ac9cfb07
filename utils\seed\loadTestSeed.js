const bcrypt = require("bcryptjs");
const { v4: uuidv4 } = require("uuid");

/**
 * Load Testing Seed - Creates 500 players, 100 clubs, 100 arbiters, 200 tournaments
 * with 100,000 registrations and payments (500 per tournament) for comprehensive load testing
 */

/**
 * Generate load testing data
 * @param {Object} models - The models object from the database configuration
 * @param {Object} options - Options for seeding
 * @returns {Promise<void>}
 */
const loadTestSeed = async (models, options = {}) => {
    try {
        console.log("Starting load testing database seeding...");

        // Create users (500 players, 100 clubs, 100 arbiters)
        const users = await createLoadTestUsers(models.User, options);
        console.log("✅ Load test users created successfully");

        // Create club details for all club users
        await createLoadTestClubDetails(models.ClubDetail, users.clubUsers, options);
        console.log("✅ Load test club details created successfully");

        // Create player details for all player users
        await createLoadTestPlayerDetails(models.PlayerDetail, users.playerUsers, users.clubUsers, options);
        console.log("✅ Load test player details created successfully");

        // Create arbiter details for all arbiter users
        await createLoadTestArbiterDetails(models.ArbiterDetails, users.arbiterUsers, options);
        console.log("✅ Load test arbiter details created successfully");

        // Create tournaments (200 tournaments)
        const createdTournaments = await createLoadTestTournaments(models.Tournament, users.clubUsers, users.arbiterUsers, options);

        // Get all load test tournaments (including existing ones)
        const allTournaments = await models.Tournament.findAll({
            where: {
                title: {
                    [require('sequelize').Op.like]: 'loadtest-%'
                }
            }
        });
        console.log(`✅ Load test tournaments ready: ${allTournaments.length} total tournaments`);

        // Create registrations and payments
        await createLoadTestRegistrationsAndPayments(
            models.Registration,
            models.Payment,
            models.BulkRegistration,
            users.playerUsers,
            users.clubUsers,
            allTournaments,
            options
        );
        console.log("✅ Load test registrations and payments created successfully");

        console.log("Load testing database seeding completed successfully!");
    } catch (error) {
        console.error("Error in load testing seeding:", error);
        throw error;
    }
};

/**
 * Create load test users (500 players, 100 clubs, 100 arbiters)
 */
const createLoadTestUsers = async (User, options = {}) => {
    const hashedPassword = await bcrypt.hash("password123", 10);
    const userData = [];

    // Create 1 admin user
    userData.push({
        name: "Load Test Admin",
        cbid: "LTADMIN001",
        email: "<EMAIL>",
        phoneNumber: "9000000000",
        password: hashedPassword,
        role: "admin",
    });

    // Create 100 arbiters
    for (let i = 1; i <= 100; i++) {
        userData.push({
            name: `Load Test Arbiter ${i}`,
            cbid: `LTA${i.toString().padStart(3, "0")}`,
            email: `loadtest.arbiter${i}@chessbrigade.com`,
            phoneNumber: `90${(******** + i).toString()}`,
            password: hashedPassword,
            role: "arbiter",
        });
    }

    // Create 100 clubs
    const clubNames = [
        "Mumbai", "Delhi", "Bangalore", "Chennai", "Kolkata", "Hyderabad", "Pune", "Ahmedabad",
        "Jaipur", "Lucknow", "Kanpur", "Nagpur", "Indore", "Thane", "Bhopal", "Visakhapatnam",
        "Pimpri", "Patna", "Vadodara", "Ghaziabad", "Ludhiana", "Agra", "Nashik", "Faridabad",
        "Meerut", "Rajkot", "Kalyan", "Vasai", "Varanasi", "Srinagar", "Aurangabad", "Dhanbad",
        "Amritsar", "Navi Mumbai", "Allahabad", "Ranchi", "Howrah", "Coimbatore", "Jabalpur", "Gwalior",
        "Vijayawada", "Jodhpur", "Madurai", "Raipur", "Kota", "Guwahati", "Chandigarh", "Solapur",
        "Hubli", "Tiruchirappalli", "Bareilly", "Mysore", "Tiruppur", "Gurgaon", "Aligarh", "Jalandhar",
        "Bhubaneswar", "Salem", "Warangal", "Guntur", "Bhiwandi", "Saharanpur", "Gorakhpur", "Bikaner",
        "Amravati", "Noida", "Jamshedpur", "Bhilai", "Cuttack", "Firozabad", "Kochi", "Nellore",
        "Bhavnagar", "Dehradun", "Durgapur", "Asansol", "Rourkela", "Nanded", "Kolhapur", "Ajmer",
        "Akola", "Gulbarga", "Jamnagar", "Ujjain", "Loni", "Siliguri", "Jhansi", "Ulhasnagar",
        "Jammu", "Sangli", "Mangalore", "Erode", "Belgaum", "Ambattur", "Tirunelveli", "Malegaon",
        "Gaya", "Jalgaon", "Udaipur", "Maheshtala", "Davanagere", "Kozhikode", "Kurnool", "Rajpur"
    ];

    for (let i = 1; i <= 100; i++) {
        const cityName = clubNames[i - 1] || `City${i}`;
        userData.push({
            name: `${cityName} Chess Club`,
            cbid: `LTC${i.toString().padStart(3, "0")}`,
            email: `loadtest.club${i}@chessbrigade.com`,
            phoneNumber: `91${(******** + i).toString()}`,
            password: hashedPassword,
            role: "club",
        });
    }

    // Create 500 players
    const firstNames = [
        "Aarav", "Vivaan", "Aditya", "Vihaan", "Arjun", "Sai", "Reyansh", "Ayaan", "Krishna", "Ishaan",
        "Shaurya", "Atharv", "Advik", "Pranav", "Vedant", "Kabir", "Shivansh", "Arnav", "Siddharth", "Abhimanyu",
        "Aadhya", "Diya", "Kavya", "Ananya", "Anika", "Saanvi", "Ira", "Myra", "Tara", "Zara",
        "Kiara", "Shanaya", "Aarohi", "Riya", "Navya", "Pihu", "Prisha", "Anvi", "Vanya", "Khushi"
    ];
    
    const lastNames = [
        "Sharma", "Verma", "Gupta", "Singh", "Kumar", "Agarwal", "Jain", "Bansal", "Goel", "Mittal",
        "Chopra", "Malhotra", "Arora", "Kapoor", "Bhatia", "Sethi", "Khanna", "Sood", "Tandon", "Saxena",
        "Agrawal", "Goyal", "Jindal", "Singhal", "Mahajan", "Khurana", "Tiwari", "Pandey", "Mishra", "Shukla"
    ];

    for (let i = 1; i <= 500; i++) {
        const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
        const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
        userData.push({
            name: `${firstName} ${lastName}`,
            cbid: `LTCB25US${i.toString().padStart(5, "0")}`,
            email: `loadtest.player${i}@chessbrigade.com`,
            phoneNumber: `92${(******** + i).toString()}`,
            password: hashedPassword,
            role: "player",
        });
    }

    // Check for existing users to avoid duplicates
    const existingUsers = await User.findAll({
        attributes: ["email", "cbid"],
    });

    const existingEmails = new Set(existingUsers.map(user => user.email));
    const existingCbids = new Set(existingUsers.map(user => user.cbid));

    const filteredUserData = userData.filter(user => {
        return !existingEmails.has(user.email) && !existingCbids.has(user.cbid);
    });

    let createdUsers = [];
    if (filteredUserData.length > 0) {
        createdUsers = await User.bulkCreate(filteredUserData, {
            ignoreDuplicates: options.ignoreDuplicates || true,
        });
        console.log(`Created ${createdUsers.length} new load test users`);
    }

    // Get all users for relationships
    const allUsers = await User.findAll({
        where: {
            email: {
                [require('sequelize').Op.like]: '<EMAIL>'
            }
        }
    });

    const adminUsers = allUsers.filter(user => user.role === "admin");
    const clubUsers = allUsers.filter(user => user.role === "club");
    const playerUsers = allUsers.filter(user => user.role === "player");
    const arbiterUsers = allUsers.filter(user => user.role === "arbiter");

    return { adminUsers, clubUsers, playerUsers, arbiterUsers, allUsers };
};

/**
 * Create load test club details
 */
const createLoadTestClubDetails = async (ClubDetail, clubUsers, options = {}) => {
    const existingClubDetails = await ClubDetail.findAll({
        attributes: ["userId"],
    });

    const existingUserIds = new Set(existingClubDetails.map(club => club.userId));
    const filteredClubUsers = clubUsers.filter(user => !existingUserIds.has(user.id));

    if (filteredClubUsers.length === 0) {
        console.log("No new load test club details to create");
        return [];
    }

    const states = [
        "Maharashtra", "Delhi", "Karnataka", "Tamil Nadu", "West Bengal", "Telangana", "Gujarat", "Rajasthan",
        "Uttar Pradesh", "Madhya Pradesh", "Bihar", "Odisha", "Punjab", "Haryana", "Kerala", "Assam"
    ];

    const clubDetailsData = filteredClubUsers.map((clubUser, index) => {
        const state = states[index % states.length];
        const clubName = clubUser.name;
        const city = clubName.split(" ")[0];
        
        return {
            clubName,
            clubId: clubName.toLowerCase().replace(/\s+/g, "-"),
            clubDistrictId: `LT${city.toUpperCase().slice(0, 3)}${index.toString().padStart(3, "0")}`,
            userId: clubUser.id,
            tournamentStatus: "inactive",
            contactPersonName: `Contact Person ${index + 1}`,
            contactPersonNumber: `93${(******** + index).toString()}`,
            contactPersonEmail: `contact${index + 1}@loadtest.chessbrigade.com`,
            alternateContactNumber: `94${(******** + index).toString()}`,
            country: "India",
            state,
            district: city,
            city,
            pincode: `${400000 + index}`,
            address: `${index + 1} Load Test Street, ${city}, ${state}`,
            locationUrl: `https://maps.google.com/maps?q=${encodeURIComponent(city + " " + state)}`,
            authorizedSignatoryName: `Signatory ${index + 1}`,
            authorizedSignatoryContactNumber: `95${(******** + index).toString()}`,
            authorizedSignatoryEmail: `signatory${index + 1}@loadtest.chessbrigade.com`,
            authorizedSignatoryDesignation: "President",
            bankName: "State Bank of India",
            AccountNumber: `${********00 + index}`,
            branchIFSCCode: `SBIN000${(1000 + index).toString()}`,
            branchName: `SBI ${city} Branch`,
            spotEntry: Math.random() < 0.3,
            bankAccountType: "Current",
            bankAccountHolderName: clubName,
            countryCode: "IN",
            stateCode: state.slice(0, 2).toUpperCase(),
            profileUrl: `https://example.com/loadtest/clubs/${index + 1}.jpg`,
        };
    });

    return await ClubDetail.bulkCreate(clubDetailsData, {
        ignoreDuplicates: options.ignoreDuplicates ?? true,
    });
};

/**
 * Create load test player details
 */
const createLoadTestPlayerDetails = async (PlayerDetail, playerUsers, clubUsers, options = {}) => {
    const existingPlayerDetails = await PlayerDetail.findAll({ attributes: ["userId"] });
    const existingUserIds = new Set(existingPlayerDetails.map(player => player.userId));

    const filteredPlayerUsers = playerUsers.filter(user => !existingUserIds.has(user.id));

    if (filteredPlayerUsers.length === 0) {
        console.log("No new load test player details to create");
        return [];
    }

    // Get club details for random assignment
    const { ClubDetail } = require("../../config/db").models;
    const clubDetails = await ClubDetail.findAll({
        where: {
            userId: {
                [require('sequelize').Op.in]: clubUsers.map(club => club.id)
            }
        }
    });

    const playerDetailsData = filteredPlayerUsers.map((playerUser, index) => {
        const randomClub = clubDetails[index % clubDetails.length];
        const genders = ["male", "female"];
        const randomGender = genders[Math.floor(Math.random() * genders.length)];
        const randomDate = new Date(1990 + Math.floor(Math.random() * 20), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);

        const states = [
            "Maharashtra", "Delhi", "Karnataka", "Tamil Nadu", "West Bengal", "Telangana", "Gujarat", "Rajasthan",
            "Uttar Pradesh", "Madhya Pradesh", "Bihar", "Odisha", "Punjab", "Haryana", "Kerala", "Assam"
        ];

        const cities = [
            "Mumbai", "Delhi", "Bangalore", "Chennai", "Kolkata", "Hyderabad", "Pune", "Ahmedabad",
            "Jaipur", "Lucknow", "Kanpur", "Nagpur", "Indore", "Thane", "Bhopal", "Visakhapatnam"
        ];

        const state = states[index % states.length];
        const city = cities[index % cities.length];

        return {
            playerTitle: index % 10 === 0 ? "GM" : index % 5 === 0 ? "IM" : null,
            playerName: playerUser.name,
            profileUrl: `https://example.com/loadtest/players/${index + 1}.jpg`,
            userId: playerUser.id,
            dob: randomDate.toISOString().split("T")[0],
            gender: randomGender,
            parentGuardianName: `Parent ${index + 1}`,
            emergencyContact: `96${(******** + index).toString()}`,
            alternateContact: `97${(******** + index).toString()}`,
            fideRating: `${1800 + Math.floor(Math.random() * 800)}`,
            fideId: `${6000000 + index}`,
            aicfId: `LTAICF${String(index + 1).padStart(5, "0")}`,
            stateId: `LTSTATE${index + 1}`,
            districtId: `LTDIST${index + 1}`,
            association: "All India Chess Federation",
            club: randomClub ? randomClub.clubName : null,
            clubId: randomClub ? randomClub.id : null,
            country: "India",
            state: state,
            district: city,
            city: city,
            pincode: `${500000 + index}`,
            address: `${index + 1} Load Test Player Street, ${city}, ${state}`,
            termsAndConditions: true,
        };
    });

    return await PlayerDetail.bulkCreate(playerDetailsData, {
        ignoreDuplicates: options.ignoreDuplicates || true,
    });
};

/**
 * Create load test arbiter details
 */
const createLoadTestArbiterDetails = async (ArbiterDetails, arbiterUsers, options = {}) => {
    const existingArbiters = await ArbiterDetails.findAll({
        attributes: ["userId"],
    });

    const existingUserIds = new Set(existingArbiters.map(a => a.userId));
    const filteredUsers = arbiterUsers.filter(user => !existingUserIds.has(user.id));

    if (filteredUsers.length === 0) {
        console.log("No new load test arbiter details to create");
        return [];
    }

    const titles = ["GM", "IM", "FM", "WGM", "WIM", "WFM", "NA"];
    const states = [
        "Maharashtra", "Delhi", "Karnataka", "Tamil Nadu", "West Bengal", "Telangana", "Gujarat", "Rajasthan",
        "Uttar Pradesh", "Madhya Pradesh", "Bihar", "Odisha", "Punjab", "Haryana", "Kerala", "Assam"
    ];

    const cities = [
        "Mumbai", "Delhi", "Bangalore", "Chennai", "Kolkata", "Hyderabad", "Pune", "Ahmedabad",
        "Jaipur", "Lucknow", "Kanpur", "Nagpur", "Indore", "Thane", "Bhopal", "Visakhapatnam"
    ];

    const arbiterDetailsData = filteredUsers.map((user, index) => ({
        userId: user.id,
        profileUrl: `https://example.com/loadtest/arbiters/${index + 1}.jpg`,
        officialId: `LTARB${(2000 + index).toString()}`,
        title: titles[index % titles.length],
        phoneNumber: `98${(******** + index).toString()}`,
        email: `loadtest.arbiter${index + 1}@chessbrigade.com`,
        alternateContactNumber: `99${(******** + index).toString()}`,
        country: "India",
        state: states[index % states.length],
        district: cities[index % cities.length],
        city: cities[index % cities.length],
        pincode: `${600000 + index}`,
    }));

    return await ArbiterDetails.bulkCreate(arbiterDetailsData, {
        ignoreDuplicates: options.ignoreDuplicates || true,
    });
};

/**
 * Create load test tournaments (200 tournaments)
 */
const createLoadTestTournaments = async (Tournament, clubUsers, arbiterUsers, options = {}) => {
    const existingTournaments = await Tournament.findAll({
        attributes: ["title"],
    });

    const existingTitles = new Set(existingTournaments.map(tournament => tournament.title));
    const tournamentData = [];

    const levels = ["national", "state", "district", "global"];
    const systems = ["swiss-system", "round-robin", "knockout"];
    const categories = ["open", "male", "female"];

    // Create 2 tournaments per club (100 clubs * 2 = 200 tournaments)
    clubUsers.forEach((clubUser, clubIndex) => {
        for (let i = 0; i < 2; i++) {
            const tournamentTitle = `loadtest-${clubUser.name.toLowerCase().replace(/\s+/g, "-")}-championship-${i + 1}`;

            if (existingTitles.has(tournamentTitle)) {
                console.log(`Skipping existing tournament: ${tournamentTitle}`);
                continue;
            }

            const {
                startDate,
                endDate,
                registrationStartDate,
                registrationEndDate,
            } = generateTournamentDates();

            const category = categories[Math.floor(Math.random() * categories.length)];
            const randomArbiter = arbiterUsers[Math.floor(Math.random() * arbiterUsers.length)];

            tournamentData.push({
                clubId: clubUser.id,
                title: tournamentTitle,
                subTitle: `Load Test Sub-${tournamentTitle}`,
                presentedBy: `Presented by ${clubUser.name}`,
                fideRated: i === 0, // First tournament is FIDE rated
                organizerName: clubUser.name,
                tournamentLevel: levels[Math.floor(Math.random() * levels.length)],
                startDate: startDate,
                endDate: endDate,
                registrationStartDate: registrationStartDate,
                registrationEndDate: registrationEndDate,
                registrationEndTime: "11:59 PM",
                arbiterId: randomArbiter.id,
                tournamentDirectorName: `Tournament Director ${clubIndex + 1}`,
                entryFeeCurrency: "INR",
                entryFee: [300, 500, 750, 1000][Math.floor(Math.random() * 4)],
                timeControl: "classical",
                reportingTime: "09:00 AM",
                timeControlDuration: "90",
                timeControlIncrement: "30",
                tournamentType: "individual",
                tournamentSystem: systems[Math.floor(Math.random() * systems.length)],
                nationalApproval: "Approved",
                stateApproval: "Approved",
                districtApproval: "Approved",
                contactPersonName: `Contact Person ${clubIndex + 1}`,
                email: `tournament${i + 1}@loadtest.chessbrigade.com`,
                contactNumber: clubUser.phoneNumber,
                alternateContactNumber: `87${(******** + clubIndex).toString()}`,
                numberOfTrophiesMale: 3,
                numberOfTrophiesFemale: 3,
                totalCashPrizeCurrency: "INR",
                totalCashPrizeAmount: [5000, 10000, 15000, 20000][Math.floor(Math.random() * 4)],
                country: "India",
                state: ["Maharashtra", "Delhi", "Karnataka", "Tamil Nadu"][clubIndex % 4],
                district: ["Mumbai", "Delhi", "Bangalore", "Chennai"][clubIndex % 4],
                city: ["Mumbai", "Delhi", "Bangalore", "Chennai"][clubIndex % 4],
                pincode: `${400000 + clubIndex}`,
                venueAddress: `${clubIndex + 1} Load Test Venue Street, Load Test City`,
                nearestLandmark: "Load Test Landmark",
                brochureUrl: `https://example.com/loadtest/tournaments/${clubIndex + 1}-${i + 1}.pdf`,
                locationUrl: `https://maps.google.com/maps?q=loadtest+venue+${clubIndex + 1}`,
                chessboardProvided: Math.random() < 0.7,
                timerProvided: Math.random() < 0.8,
                parkingFacility: ["yes", "no", "limited"][Math.floor(Math.random() * 3)],
                spotEntry: Math.random() < 0.2,
                tournamentStatus: "active", // Make tournaments active for registrations
                foodFacility: ["breakfast", "lunch", "dinner", "snacks", "beverages"]
                    .sort(() => 0.5 - Math.random())
                    .slice(0, Math.floor(Math.random() * 3) + 1)
                    .join(","),
                tournamentCategory: category,
                maleAgeCategory: category === 'male' || category === 'open' ?
                    ["OPEN", "U18", "U15", "U12"].sort(() => 0.5 - Math.random()).slice(0, Math.floor(Math.random() * 4) + 1) : null,
                femaleAgeCategory: category === 'female' || category === 'open' ?
                    ["OPEN", "U18", "U15", "U12"].sort(() => 0.5 - Math.random()).slice(0, Math.floor(Math.random() * 4) + 1) : null,
            });
        }
    });

    if (tournamentData.length === 0) {
        console.log("No new load test tournaments to create");
        return [];
    }

    const createdTournaments = await Tournament.bulkCreate(tournamentData, {
        ignoreDuplicates: options.ignoreDuplicates || true,
    });

    console.log(`Created ${createdTournaments.length} load test tournaments`);
    return createdTournaments;
};

/**
 * Generate tournament dates
 */
const generateTournamentDates = () => {
    const today = new Date();
    const monthOffset = Math.random() < 0.5 ? 0 : 1;
    const year = today.getFullYear();
    const month = today.getMonth() + monthOffset;

    const startDay = Math.floor(Math.random() * 20) + 5;
    const startDate = new Date(year, month, startDay);

    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + Math.floor(Math.random() * 3) + 1);

    const registrationEndDate = new Date(startDate);
    registrationEndDate.setDate(startDate.getDate() - (Math.floor(Math.random() * 3) + 1));

    const registrationStartDate = new Date(registrationEndDate);
    registrationStartDate.setDate(registrationEndDate.getDate() - (Math.floor(Math.random() * 5) + 2));

    const format = (d) => d.toISOString().split("T")[0];

    return {
        startDate: format(startDate),
        endDate: format(endDate),
        registrationStartDate: format(registrationStartDate),
        registrationEndDate: format(registrationEndDate),
    };
};

/**
 * Create load test registrations and payments
 * 500 players registered to 200 tournaments with payments
 */
const createLoadTestRegistrationsAndPayments = async (
    Registration,
    Payment,
    BulkRegistration,
    playerUsers,
    clubUsers,
    tournaments,
    options = {}
) => {
    console.log("Creating load test registrations and payments...");

    const registrationData = [];
    const paymentData = [];
    const bulkRegistrationData = [];

    // Get club details for bulk registrations
    const { ClubDetail } = require("../../config/db").models;
    const clubDetails = await ClubDetail.findAll({
        where: {
            userId: {
                [require('sequelize').Op.in]: clubUsers.map(club => club.id)
            }
        }
    });

    console.log(`Found ${clubDetails.length} club details for ${clubUsers.length} club users`);

    // Create 500 player registrations for EACH tournament
    // Since we have 500 players and need 500 registrations per tournament,
    // each player will register for exactly one tournament to avoid unique constraint violations
    let registrationCounter = 0;

    console.log(`Creating registrations: ${playerUsers.length} players across ${tournaments.length} tournaments`);

    for (let tournamentIndex = 0; tournamentIndex < tournaments.length; tournamentIndex++) {
        const tournament = tournaments[tournamentIndex];
        console.log(`Creating 500 registrations for tournament: ${tournament.title}`);

        // Use all 500 players for this tournament (each player registers once per tournament)
        for (let i = 0; i < playerUsers.length; i++) {
            const player = playerUsers[i];

            const registrationId = uuidv4();
            const paymentId = uuidv4();
            registrationCounter++;

            // Create payment first (without registration reference)
            const razorpayOrderId = `order_LT${generateRandomString(14)}`;
            const razorpayPaymentId = `pay_LT${generateRandomString(14)}`;
            const razorpaySignature = generateRandomHash();

            paymentData.push({
                id: paymentId,
                userId: player.id,
                tournamentId: tournament.id,
                registrationId: null, // Set to null for individual payments
                bulkRegistrationId: null,
                razorpayOrderId: razorpayOrderId,
                razorpayPaymentId: razorpayPaymentId,
                razorpaySignature: razorpaySignature,
                paymentDate: new Date(),
                paymentStatus: "captured",
                paymentTransactionId: `LT-${generateRandomString(8)}`,
                paymentAmount: tournament.entryFee,
                paymentCurrency: "INR",
                paymentMethod: ["upi", "card", "netbanking"][Math.floor(Math.random() * 3)],
                paymentReference: razorpayPaymentId,
                paymentType: "player",
                paymentRemarks: "Payment captured",
                razorpayResponse: generateMockRazorpayResponse(player, tournament, razorpayPaymentId, razorpayOrderId, tournament.entryFee),
                razorpayOrderStatus: "paid",
                razorpayAttempts: 1,
            });

            // Create registration
            const ageCategories = tournament.maleAgeCategory || tournament.femaleAgeCategory || ["OPEN"];
            const genderCategories = tournament.tournamentCategory === "open" ? ["male", "female"] : [tournament.tournamentCategory];

            registrationData.push({
                id: registrationId,
                tournamentId: tournament.id,
                regId: `LTR${(100000 + registrationCounter).toString()}`,
                playerId: player.id,
                registeredDate: new Date(),
                ageCategory: ageCategories[Math.floor(Math.random() * ageCategories.length)],
                genderCategory: genderCategories[Math.floor(Math.random() * genderCategories.length)],
                tournamentTitle: tournament.title,
                registrationRemarks: `Load test registration ${registrationCounter}`,
                paymentId: paymentId,
                attendanceMark: null,
                paymentAmount: tournament.entryFee,
                paymentDate: new Date(),
                status: "active",
            });
        }
    }

    console.log(`Total registrations created: ${registrationData.length}`);
    console.log(`Total payments created: ${paymentData.length}`);

    // Bulk create all data - PAYMENTS FIRST due to foreign key constraints
    console.log(`Creating ${paymentData.length} payments in batches...`);
    if (paymentData.length > 0) {
        const batchSize = 1000;
        for (let i = 0; i < paymentData.length; i += batchSize) {
            const batch = paymentData.slice(i, i + batchSize);
            console.log(`Inserting payment batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(paymentData.length/batchSize)} (${batch.length} records)`);
            await Payment.bulkCreate(batch, {
                ignoreDuplicates: options.ignoreDuplicates || true,
            });
        }
    }

    console.log(`Creating ${registrationData.length} individual registrations in batches...`);
    if (registrationData.length > 0) {
        const batchSize = 1000;
        for (let i = 0; i < registrationData.length; i += batchSize) {
            const batch = registrationData.slice(i, i + batchSize);
            console.log(`Inserting registration batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(registrationData.length/batchSize)} (${batch.length} records)`);
            await Registration.bulkCreate(batch, {
                ignoreDuplicates: options.ignoreDuplicates || true,
            });
        }
    }

    // Skip bulk registrations for now - simplified load test
    console.log("Skipping bulk registrations for simplified load test");

    console.log("Load test registrations and payments created successfully!");
};

/**
 * Generate random string for IDs
 */
const generateRandomString = (length) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
};

/**
 * Generate random hash for signatures
 */
const generateRandomHash = () => {
    return require('crypto').randomBytes(32).toString('hex');
};

/**
 * Generate mock Razorpay response for individual payments
 */
const generateMockRazorpayResponse = (player, tournament, paymentId, orderId, amount) => {
    const amountInPaise = amount * 100;
    const fee = Math.floor(amountInPaise * 0.024); // 2.4% fee
    const tax = Math.floor(fee * 0.18); // 18% tax on fee

    return {
        id: paymentId,
        fee: fee,
        tax: tax,
        upi: { vpa: "success@razorpay" },
        vpa: "success@razorpay",
        bank: null,
        email: player.email,
        notes: {
            userId: player.id,
            paymentId: paymentId,
            playerName: player.name,
            paymentType: "player",
            playerEmail: player.email,
            playerPhone: player.phoneNumber,
            tournamentId: tournament.id,
            tournamentTitle: tournament.title,
            eligibleCategory: "male-OPEN"
        },
        amount: amountInPaise,
        entity: "payment",
        method: "upi",
        status: "captured",
        wallet: null,
        card_id: null,
        contact: `+91${player.phoneNumber}`,
        captured: true,
        currency: "INR",
        order_id: orderId,
        created_at: Math.floor(Date.now() / 1000),
        error_code: null,
        error_step: null,
        invoice_id: null,
        description: `Registration for ${tournament.title}`,
        error_reason: null,
        error_source: null,
        acquirer_data: {
            rrn: generateRandomString(12),
            upi_transaction_id: generateRandomString(32).toUpperCase()
        },
        international: false,
        refund_status: null,
        amount_refunded: 0,
        formattedAmount: amount,
        error_description: null
    };
};

/**
 * Generate mock Razorpay response for bulk payments
 */
const generateMockBulkRazorpayResponse = (club, tournament, paymentId, orderId, amount, playerCount) => {
    const amountInPaise = amount * 100;
    const fee = Math.floor(amountInPaise * 0.024); // 2.4% fee
    const tax = Math.floor(fee * 0.18); // 18% tax on fee

    return {
        id: paymentId,
        fee: fee,
        tax: tax,
        upi: { vpa: "success@razorpay" },
        vpa: "success@razorpay",
        bank: null,
        email: club.email,
        notes: {
            userId: club.id,
            clubName: club.name,
            clubEmail: club.email,
            clubPhone: club.phoneNumber,
            paymentId: paymentId,
            paymentType: "club",
            playerCount: playerCount.toString(),
            tournamentId: tournament.id,
            tournamentTitle: tournament.title,
            bulkRegistrationId: `ltbr-${Math.floor(Math.random() * 1000)}`
        },
        amount: amountInPaise,
        entity: "payment",
        method: "upi",
        status: "captured",
        wallet: null,
        card_id: null,
        contact: `+91${club.phoneNumber}`,
        captured: true,
        currency: "INR",
        order_id: orderId,
        created_at: Math.floor(Date.now() / 1000),
        error_code: null,
        error_step: null,
        invoice_id: null,
        description: `Bulk Registration for ${tournament.title} (${playerCount} players)`,
        error_reason: null,
        error_source: null,
        acquirer_data: {
            rrn: generateRandomString(12),
            upi_transaction_id: generateRandomString(32).toUpperCase()
        },
        international: false,
        refund_status: null,
        amount_refunded: 0,
        formattedAmount: amount,
        error_description: null
    };
};

module.exports = loadTestSeed;
