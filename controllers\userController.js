const { User, InviteRequest, PlayerDetail, ClubDetail,Advertisement } =
  require("../config/db").models;
const { Sequelize, Op } = require("sequelize");
const { sendResponse, handleError } = require("../utils/apiResponse");
const {
  friendRequestSchema,
  updateFriendRequestSchema,
} = require("../schema/friendSchema");
const emailService = require("../utils/mailer/emailService");
const { config } = require("../config/config");

const getUserDetails = async (req, res) => {
  try {
    const email = req.user.email;
    if (!email) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }

    const user = await User.findOne({
      where: { email: email },
      attributes: ["email", "phoneNumber", "cbid", "name"],
    });
    if (!user) {
      return sendResponse(res, 404, {
        success: false,
        error: "User not found",
      });
    }
    sendResponse(res, 200, {
      success: true,
      data: user,
    });
  } catch (error) {
    handleError(res, error);
  }
};
const getClubInvite = async (req, res) => {
  try {
    const userId = req.user.userId;
    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }
    const invite = await InviteRequest.findAll({
      where: { userId: userId, type: "club-invite" },
    });
    if (!invite) {
      return sendResponse(res, 204, {
        success: false,
        error: "No invite found",
      });
    }
    sendResponse(res, 200, {
      success: true,
      data: invite,
    });
  } catch (error) {
    handleError(res, error);
  }
};
const updateClubInvite = async (req, res) => {
  try {
    const { action, clubId } = req.body;

    const userId = req.user.userId;
    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }

    if (!clubId) {
      return sendResponse(res, 422, {
        success: false,
        error: "Club id is required",
      });
    }

    const invite = await InviteRequest.findOne({
      where: {
        userId: userId,
        type: "club-invite",
        [Op.and]: [
          Sequelize.where(
            Sequelize.json("metadata.club.clubId"), // 🧠 access inside JSON
            clubId
          ),
        ],
      },
    });
    if (!invite) {
      return sendResponse(res, 204, {
        success: false,
        error: "No invite found",
      });
    }
    const user = await PlayerDetail.findOne({
      where: { userId: userId },
      include: [{ model: User, attributes: ["email"] }],
    });
    if (!user) {
      return sendResponse(res, 404, {
        success: false,
        error: "User not found",
      });
    }
    if (action === "reject") {
      await invite.destroy();
      return sendResponse(res, 200, {
        success: true,
        message: "Club invite rejected",
      });
    }
    if (action === "accept") {
      if (user.clubId !== null) {
        return sendResponse(res, 409, {
          success: false,
          error: "User already in a club",
        });
      }

      user.clubId = clubId;
      user.club = invite.metadata.club.clubName;
      await user.save();
      emailService.sendClubAcceptRequestEmail({
        email: invite.metadata.club.clubEmail,
        subject: `${invite.metadata.club.clubName} has accepted your request to join`,
        playerName: user.name,
        clubName: invite.metadata.club.clubName,
        clubProfileUrl: `${config.frontend_url}/dashboard/}`,
        
      });
      await invite.destroy();

      return sendResponse(res, 200, {
        success: true,
        message: "Club invite accepted",
      });
    }
  } catch (error) {
    handleError(res, error);
  }
};
const leaveClub = async (req, res) => {
  try {
    const userId = req.user.userId;
    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }
    const user = await PlayerDetail.findOne({
      where: { userId: userId },
    });
    if (!user) {
      return sendResponse(res, 404, {
        success: false,
        error: "User not found",
      });
    }
    if (user.clubId === null) {
      return sendResponse(res, 409, {
        success: false,
        error: "User not in a club",
      });
    }
    const club = await ClubDetail.findOne({
      where: { id: user.clubId },
      include: [{ model: User, as: "user", attributes: ["email"] }],
    });
    if (!club) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club not found",
      });
    }

    user.clubId = null;
    user.club = null;
    await user.save();
    emailService.sendPlayerLeaveClubEmail({
      email: club.user.email,
      subject: `${user.name} has left your club`,
      playerName: user.name,
      clubName: user.club,
      message: req.body.message || "",
    });
    return sendResponse(res, 200, {
      success: true,
      message: "Club left successfully",
    });
  } catch (error) {
    handleError(res, error);
  }
};
const SendclubInvite = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { message, clubId, clubName } = req.body;
    if (!clubId || !clubName || !message) {
      return sendResponse(res, 400, {
        success: false,
        error: "Club id, club name and message is required",
      });
    }
    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }

    const user = await User.findOne({
      where: { id: userId, role: "player" },
      include: [PlayerDetail],
    });

    if (!user) {
      return sendResponse(res, 404, {
        success: false,
        error: "User not found",
      });
    }
    if (!user.PlayerDetail || !user.PlayerDetail.id) {
      return sendResponse(res, 400, {
        success: false,
        error: "Please complete your profile",
      });
    }
    if (user.PlayerDetail.clubId !== null) {
      return sendResponse(res, 409, {
        success: false,
        error: "You are already in a club,please leave it first",
      });
    }
    const club = await ClubDetail.findOne({
      where: { clubId: clubId },
      include: [{ model: User, as: "user", attributes: ["email"] }],
    });
    if (!club) {
      return sendResponse(res, 404, {
        success: false,
        error: "Club not found",
      });
    }
    const [inviteRequest, created] = await InviteRequest.findOrCreate({
      where: {
        userId: club.userId,
        type: "join-request",
        [Op.and]: [
          Sequelize.where(
            Sequelize.json("metadata.player.playerId"), // 🧠 access inside JSON
            user.PlayerDetail.id // 🧠 compare value
          ),
        ],
      },
      defaults: {
        userId: club.userId,
        type: "join-request",
        title: "club jion request",
        message: `${user.name}, has requested to join your club!`,
        metadata: {
          club: {
            clubName: club.clubName,
            clubId: club.id,
            clubEmail: club.user.email,
          },
          player: {
            playerName: user.name,
            playerId: user.PlayerDetail.id,
            userId: user.id,
            cbid: user.cbid,
            email: user.email,
          },
          message,
        },
      },
    });
    if (!created) {
      return sendResponse(res, 409, {
        success: false,
        error: "join request already sent",
      });
    }
    emailService.sendClubPlayerInviteEmail({
      clubEmail: club.user.email,
      playerName: user.name,
      clubName: club.clubName,
      message,
      inviteUrl: `${config.frontend_url}/dashboard`,
    });

    return sendResponse(res, 200, {
      success: true,
      message: "Join request sent successfully",
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * Send a friend request to another player
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const sendFriendRequest = async (req, res) => {
  try {
    const userId = req.user.userId;

    // Validate input
    const { success, data, error } = friendRequestSchema.safeParse(req.body);
    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: error.errors,
      });
    }

    const { friendId, message } = data;

    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }

    // Check if user is trying to send request to themselves
    if (userId === friendId) {
      return sendResponse(res, 400, {
        success: false,
        error: "Cannot send friend request to yourself",
      });
    }

    const user = await User.findOne({
      where: { id: userId },
      attributes: ["name", "id","email"],
    });

    if (!user) {
      return sendResponse(res, 404, {
        success: false,
        error: "User not found",
      });
    }

    const friend = await User.findOne({
      where: { id: friendId },
      attributes: ["name", "id","email"],
    });

    if (!friend) {
      return sendResponse(res, 404, {
        success: false,
        error: "Friend not found",
      });
    }

    // Check if they are already friends
    const playerDetail = await PlayerDetail.findOne({ where: { userId } });
    if (playerDetail && playerDetail.friendIds.includes(friendId)) {
      return sendResponse(res, 409, {
        success: false,
        error: "Already friends with this user",
      });
    }

    // Check if there's already a pending request from the friend to the user
    const existingRequestFromFriend = await InviteRequest.findOne({
      where: {
        userId,
        type: "friend-request",
        [Op.and]: [
          Sequelize.where(Sequelize.json("metadata.friend.friendId"), friendId),
        ],
      },
    });

    if (existingRequestFromFriend) {
      return sendResponse(res, 409, {
        success: false,
        error:
          "This user has already sent you a friend request. Accept it instead.",
      });
    }

    // Create or find the friend request
    const [inviteRequest, created] = await InviteRequest.findOrCreate({
      where: {
        userId: friendId,
        type: "friend-request",
        [Op.and]: [
          Sequelize.where(Sequelize.json("metadata.friend.friendId"), user.id),
        ],
      },
      defaults: {
        userId: friendId,
        type: "friend-request",
        title: "Friend Request",
        message:
          message || `${user.name} has requested to add you as a friend!`,
        metadata: {
          friend: { friendName: user.name, friendId: user.id },
        },
        status: "pending",
      },
    });

    if (!created) {
      return sendResponse(res, 409, {
        success: false,
        error: "Friend request already sent",
      });
    }
    emailService.sendFriendRequestEmail({
      recipientEmail: friend.email,
      senderName: user.name,
      message: message || `${user.name} has requested to add you as a friend!`,
      actionUrl: `${config.frontend_url}/dashboard`,
    });

    return sendResponse(res, 200, {
      success: true,
      message: "Friend request sent successfully",
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * Get all friend requests for the current user
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const getFriendRequests = async (req, res) => {
  try {
    const userId = req.user.userId;

    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }

    const friendRequests = await InviteRequest.findAll({
      where: {
        userId: userId,
        type: "friend-request",
        status: { [Op.not]: ["accept", "reject"] },
      },
      order: [["createdAt", "DESC"]],
    });

    if (friendRequests.length === 0) {
      return sendResponse(res, 200, {
        success: true,
        data: [],
        message: "No friend requests found",
      });
    }

    return sendResponse(res, 200, {
      success: true,
      data: friendRequests,
    });
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * Update (accept/reject) a friend request
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const updateFriendRequest = async (req, res) => {
  try {
    const userId = req.user.userId;
    

    // Validate input
    const { success, data, error } = updateFriendRequestSchema.safeParse(
      req.body
    );
    if (!success) {
      return sendResponse(res, 422, {
        success: false,
        error: error.errors,
      });
    }

    const { friendId, action } = data;

    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }

    // Find the user and friend
    const user = await User.findOne({
      where: { id: userId },
      include: [PlayerDetail],
    });

    if (!user || !user.PlayerDetail) {
      return sendResponse(res, 404, {
        success: false,
        error: "User profile not found",
      });
    }

    const friend = await User.findOne({
      where: { id: friendId },
      include: [PlayerDetail],
    });

    if (!friend || !friend.PlayerDetail) {
      return sendResponse(res, 404, {
        success: false,
        error: "Friend profile not found",
      });
    }

    // Find the friend request
    const friendRequest = await InviteRequest.findOne({
      where: {
        userId: userId,
        type: "friend-request",
        [Op.and]: [
          Sequelize.where(Sequelize.json("metadata.friend.friendId"), friendId),
        ],
      },
    });


    if (!friendRequest) {
      return sendResponse(res, 404, {
        success: false,
        error: "Friend request not found",
      });
    }

    // Handle the action
    if (action === "reject") {
      // Just delete the request
      await friendRequest.destroy();

      return sendResponse(res, 200, {
        success: true,
        message: "Friend request rejected",
      });
    } else if (action === "accept") {
      // Add each other as friends
      if (!Array.isArray(user.PlayerDetail.friendIds)) {
        user.PlayerDetail.friendIds = [];
      }
      if (!user.PlayerDetail.friendIds.includes(friendId)) {
        user.PlayerDetail.friendIds = [
          ...user.PlayerDetail.friendIds,
          friendId,
        ]; // ✅ Reassign to trigger update
        await user.PlayerDetail.save(); // ✅ Sequelize now detects change
      }

      if (!Array.isArray(friend.PlayerDetail.friendIds)) {
        friend.PlayerDetail.friendIds = [];
      }
      if (!friend.PlayerDetail.friendIds.includes(userId)) {
        friend.PlayerDetail.friendIds = [
          ...friend.PlayerDetail.friendIds,
          userId,
        ];
        await friend.PlayerDetail.save();
      }

      // Delete the request
      await friendRequest.destroy();
      emailService.sendFriendRequestAcceptEmail({
        recipientEmail: friend.email,
        senderName: user.name,
        actionUrl: `${config.frontend_url}/dashboard`,
      });

      return sendResponse(res, 200, {
        success: true,
        message: "Friend request accepted",
      });
    }
  } catch (error) {
    handleError(res, error);
  }
};

/**
 * Remove a friend from the user's friend list
 * @param {import('express').Request} req - Express request object
 * @param {import('express').Response} res - Express response object
 */
const removeFriend = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { friendId } = req.body;

    if (!friendId) {
      return sendResponse(res, 400, {
        success: false,
        error: "Friend id is required",
      });
    }

    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }

    // Find the user and friend
    const user = await PlayerDetail.findOne({ where: { userId } });

    if (!user) {
      return sendResponse(res, 404, {
        success: false,
        error: "User not found",
      });
    }

    const friend = await PlayerDetail.findOne({ where: { userId: friendId },include: [{ model: User, attributes: ["email"] }] });

    if (!friend) {
      return sendResponse(res, 404, {
        success: false,
        error: "Friend not found",
      });
    }

    // Check if they are friends
    if (!user.friendIds.includes(friendId)) {
      return sendResponse(res, 400, {
        success: false,
        error: "This user is not in your friend list",
      });
    }

    // Remove from each other's friend lists
    user.friendIds = user.friendIds.filter((id) => id !== friendId);
    await user.save();

    friend.friendIds = friend.friendIds.filter((id) => id !== userId);
    await friend.save();
    emailService.sendFriendRemovedEmail({
      recipientEmail: friend?.User?.email,
      removedFriendName: user.name,
    });

    return sendResponse(res, 200, {
      success: true,
      message: "Friend removed successfully",
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getNotificationCount = async (req, res) => {
  try {
    const userId = req.user.userId;
    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }
    const count = await InviteRequest.count({
      where: {
        userId: userId,
        status: { [Op.not]: ["accept", "reject"] },
      },
    });
    sendResponse(res, 200, {
      success: true,
      data: count,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const checkFriendStatus = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { id: friendId } = req.params;

    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }

    if (!friendId) {
      return sendResponse(res, 400, {
        success: false,
        error: "Friend id is required",
      });
    }
    const user = await PlayerDetail.findOne({
      where: { userId },
    });

    if (!user) {
      return sendResponse(res, 404, {
        success: false,
        error: "User not found",
      });
    }

    const isFriend = user.friendIds.includes(friendId);

    return sendResponse(res, 200, {
      success: true,
      isFriend,
    });
  } catch (error) {
    handleError(res, error);
  }
};

const getAd = async(req,res)=>{
  try{
    const {userType } =req.params

    const ad =await Advertisement.findOne({where:{userType,isActive:true}})

    if(!ad){
       return sendResponse(res, 404, {
        success: false,
        error: "Ad not found",
      });
    }

     return sendResponse(res, 200, {
      success: true,
      data:ad,
    });
  }catch(e){
    handleError(res, e);
  }
}

const FindclubInvite = async (req, res) => {
  try {
    const userId = req.user.userId;
    const {id} = req.query;
   
    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }
    const inviteRequest = await InviteRequest.findOne({
      where: {
        type: "join-request",
      [Op.and]: [
          Sequelize.where(
            Sequelize.json("metadata.club.clubName"),
            id
          ),
        ],
       [Op.and]: [
          Sequelize.where(
            Sequelize.json("metadata.player.userId"),
            userId
          ),
        ],},
      });
      
    if (!inviteRequest) {
      return sendResponse(res, 201, {
        success: false,
        message: "join request already sent",
        data:[]
      });
    }

    return sendResponse(res, 200, {
      success: true,
      data:inviteRequest
    });
  } catch (error) {
    handleError(res, error);
  }
};
const CancelclubInvite = async (req, res) => {
  try {
    const userId = req.user.userId;
    const {id} = req.query;
   
    if (!userId) {
      return sendResponse(res, 401, {
        success: false,
        error: "Unauthorized",
      });
    }
    const inviteRequest = await InviteRequest.findOne({
      where: {
        type: "join-request",
      [Op.and]: [
          Sequelize.where(
            Sequelize.json("metadata.club.clubName"),
            id
          ),
        ],
       [Op.and]: [
          Sequelize.where(
            Sequelize.json("metadata.player.userId"),
            userId
          ),
        ],},
      });
    if (!inviteRequest) {
      return sendResponse(res, 201, {
        success: false,
        message: "Invite not found",
        data:[]
      });
    }
    await inviteRequest.destroy()
    return sendResponse(res, 200, {
      success: true,
      message:"Invite cancelled successfully"
    });
  } catch (error) {
    handleError(res, error);
  }
};


module.exports = {
  getUserDetails,
  getClubInvite,
  updateClubInvite,
  leaveClub,
  SendclubInvite,
  sendFriendRequest,
  getFriendRequests,
  updateFriendRequest,
  removeFriend,
  checkFriendStatus,
  getNotificationCount,
  getAd,
  FindclubInvite,
  CancelclubInvite,
};
