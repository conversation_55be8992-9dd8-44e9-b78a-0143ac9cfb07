# Ranking Import API Documentation

This document describes the API endpoints for the Ranking Import feature, which allows you to upload Excel files containing ranking data and import them into the database.

## Base URL

All endpoints are prefixed with `/api/ranking-import`.

## Authentication

All endpoints require authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your_token>
```

## Endpoints

### Upload and Process Excel File

**Endpoint:** `POST /upload`

**Description:** Upload an Excel file and process it to extract ranking data into the database.

**Request:**
- Content-Type: `multipart/form-data`

**Form Fields:**
- `file` (required): The Excel file to upload
- `tournament_id` (required): UUID of the tournament to associate the rankings with
- `round_id` (optional): Round number (default: 1)
- `category` (optional): Category of the rankings (default: 'Default')
- `deleteAfterProcessing` (optional): Set to 'true' to delete the file after processing

**Example Request:**
```bash
curl -X POST \
  -H "Authorization: Bearer <your_token>" \
  -F "file=@path/to/your/file.xlsx" \
  -F "tournament_id=123e4567-e89b-12d3-a456-************" \
  -F "round_id=1" \
  -F "category=Open" \
  -F "deleteAfterProcessing=true" \
  http://localhost:3000/api/ranking-import/upload
```

**Response:**
```json
{
  "success": true,
  "message": "File processed successfully",
  "data": {
    "recordsProcessed": 50,
    "filename": "chess_results.xlsx",
    "tournament_id": "123e4567-e89b-12d3-a456-************",
    "round_id": 1,
    "category": "Open"
  }
}
```

### Get All Rankings

**Endpoint:** `GET /`

**Description:** Get all rankings, with optional filtering by tournament ID, round ID, and category.

**Query Parameters:**
- `tournament_id` (optional): Filter by tournament ID
- `round_id` (optional): Filter by round ID
- `category` (optional): Filter by category
- `limit` (optional): Number of records to return (default: 100)
- `offset` (optional): Number of records to skip (default: 0)

**Example Request:**
```bash
curl -X GET \
  -H "Authorization: Bearer <your_token>" \
  "http://localhost:3000/api/ranking-import?tournament_id=123e4567-e89b-12d3-a456-************&round_id=1&category=Open&limit=10&offset=0"
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "tournament_id": "123e4567-e89b-12d3-a456-************",
      "round_id": 1,
      "category": "Open",
      "player_name": "John Doe",
      "fide_id": "12345678",
      "fide_rating": 2200,
      "association": "USA",
      "total_points": 7.5,
      "rank": 1,
      "tie_breakers": {
        "Buchholz": 45.5,
        "Sonneborn-Berger": 38.25
      },
      "created_at": "2023-05-01T12:00:00.000Z",
      "updated_at": "2023-05-01T12:00:00.000Z"
    },
    // More records...
  ],
  "count": 50,
  "limit": 10,
  "offset": 0
}
```

### Get Ranking by ID

**Endpoint:** `GET /:id`

**Description:** Get a single ranking by ID.

**URL Parameters:**
- `id` (required): ID of the ranking

**Example Request:**
```bash
curl -X GET \
  -H "Authorization: Bearer <your_token>" \
  "http://localhost:3000/api/ranking-import/1"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "tournament_id": "123e4567-e89b-12d3-a456-************",
    "round_id": 1,
    "category": "Open",
    "player_name": "John Doe",
    "fide_id": "12345678",
    "fide_rating": 2200,
    "association": "USA",
    "total_points": 7.5,
    "rank": 1,
    "tie_breakers": {
      "Buchholz": 45.5,
      "Sonneborn-Berger": 38.25
    },
    "created_at": "2023-05-01T12:00:00.000Z",
    "updated_at": "2023-05-01T12:00:00.000Z"
  }
}
```

### Delete Ranking by ID

**Endpoint:** `DELETE /:id`

**Description:** Delete a single ranking by ID.

**URL Parameters:**
- `id` (required): ID of the ranking

**Example Request:**
```bash
curl -X DELETE \
  -H "Authorization: Bearer <your_token>" \
  "http://localhost:3000/api/ranking-import/1"
```

**Response:**
```json
{
  "success": true,
  "message": "Ranking deleted successfully"
}
```

### Delete All Rankings for a Tournament

**Endpoint:** `DELETE /tournament/:tournament_id`

**Description:** Delete all rankings for a specific tournament.

**URL Parameters:**
- `tournament_id` (required): UUID of the tournament

**Example Request:**
```bash
curl -X DELETE \
  -H "Authorization: Bearer <your_token>" \
  "http://localhost:3000/api/ranking-import/tournament/123e4567-e89b-12d3-a456-************"
```

**Response:**
```json
{
  "success": true,
  "message": "50 rankings deleted successfully"
}
```

### Delete All Rankings for a Tournament and Round

**Endpoint:** `DELETE /tournament/:tournament_id/round/:round_id`

**Description:** Delete all rankings for a specific tournament and round.

**URL Parameters:**
- `tournament_id` (required): UUID of the tournament
- `round_id` (required): Round number

**Example Request:**
```bash
curl -X DELETE \
  -H "Authorization: Bearer <your_token>" \
  "http://localhost:3000/api/ranking-import/tournament/123e4567-e89b-12d3-a456-************/round/1"
```

**Response:**
```json
{
  "success": true,
  "message": "25 rankings deleted successfully"
}
```

## Error Responses

All endpoints return a standard error format:

```json
{
  "success": false,
  "message": "Error message describing what went wrong"
}
```

Common error status codes:
- `400`: Bad Request (e.g., missing required fields)
- `401`: Unauthorized (missing or invalid token)
- `404`: Not Found (resource not found)
- `500`: Internal Server Error (server-side error)
