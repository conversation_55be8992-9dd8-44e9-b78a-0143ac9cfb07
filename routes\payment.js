const express = require("express");
const router = express.Router();
const {
  createPaymentOrder,
  verifyPayment,
  handleWebhook,
  getPaymentStatus,
  getUserPayments,
  getClubEarnings,
  getClubPaymentsOnly,
  getPlayerPaymentsOnly,
  createBulkPaymentOrder,
  verifyBulkPayment,
  getClubPayments,
  getPendingBulkRegistrations,
  getPaymentReceipt,
  TournamentReferral,
} = require("../controllers/paymentController");
const verifyJwt = require("../middlewares/verifyJwt");
const refundRouter = require("./refund");
router.use("/refund", refundRouter);
// Payment initiation route (requires authentication)
router.post("/create-order", verifyJwt, createPaymentOrder);
router.post("/check-referral", verifyJwt,TournamentReferral);
router.post("/create-bulk-order", verifyJwt, createBulkPaymentOrder);

// Payment callback routes (no authentication required as they are called by Razorpay)
router.post("/verify-payment", verifyJwt, verifyPayment);
router.post("/verify-bulk-payment", verifyJwt, verifyBulkPayment);
router.post(
  "/webhook",
  express.raw({ type: "application/json" }),
  handleWebhook
);

// Payment status routes (requires authentication)
router.get("/club/tournament/clubpayments", verifyJwt, getClubPaymentsOnly);
router.get("/club/tournament/playerpayments", verifyJwt, getPlayerPaymentsOnly);
router.get("/status/:paymentId", verifyJwt, getPaymentStatus);
router.get("/user", verifyJwt, getUserPayments);
router.get("/club/tournament", verifyJwt, getClubEarnings);
router.get("/club", verifyJwt, getClubPayments);
router.get(
  "/club/pending-registrations",
  verifyJwt,
  getPendingBulkRegistrations
);
router.get("/receipt", verifyJwt, getPaymentReceipt);

module.exports = router;
