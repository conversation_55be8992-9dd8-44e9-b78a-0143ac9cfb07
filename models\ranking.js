// models/ranking.js
const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class Ranking extends Model {
    static associate(models) {
      Ranking.belongsTo(models.Tournament, {
        foreignKey: "tournament_id",
      });
    }
  }
  Ranking.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        allowNull: false,
        primaryKey: true,
      },
      tournament_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      round_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      age_category: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      gender_category: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      player_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      fide_id: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      fide_rating: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      association: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      total_points: {
        type: DataTypes.DECIMAL(5, 2),
        allowNull: false,
        defaultValue: 0.0,
      },
      rank: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      tie_breakers: {
        type: DataTypes.JSONB, // use JSON for MySQL
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: "Ranking",
      tableName: "rankings",
      timestamps: true,
    }
  );

  return Ranking;
};
