const z = require("zod");

const friendRequestSchema = z.object({
  friendId: z.string().uuid("Invalid UUID for friend ID"),
  message: z.string().optional(),
});

const updateFriendRequestSchema = z.object({
  friendId: z.string().uuid("Invalid UUID for friend ID"),
  action: z.enum(["accept", "reject"], {
    errorMap: () => ({ message: "Action must be either 'accept' or 'reject'" }),
  }),
});

module.exports = {
  friendRequestSchema,
  updateFriendRequestSchema,
};
