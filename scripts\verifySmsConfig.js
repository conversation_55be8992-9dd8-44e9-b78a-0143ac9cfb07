const { verifyConfiguration } = require('../utils/sms');
const { config } = require('../config/config');

async function verifySmsConfig() {
  try {
    const isValid = await verifyConfiguration();
    
    if (isValid) {
    } else {
      console.error('\n❌ SMS configuration is invalid!');
      console.error('Please check your SMS settings in the .env file.');
    }
  } catch (error) {
    console.error('\n❌ Error verifying SMS configuration:', error);
    console.error('Please check your SMS settings in the .env file.');
  }
  
  process.exit(0);
}

verifySmsConfig();
