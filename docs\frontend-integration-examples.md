# Frontend Integration Examples

This document provides example code for integrating the admin messaging system into your frontend application.

## API Service Layer

### 1. Communication API Service

```javascript
// services/communicationApi.js
import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api/v1';

class CommunicationAPI {
  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add auth token to requests
    this.api.interceptors.request.use((config) => {
      const token = localStorage.getItem('authToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });
  }

  // Template Management
  async getEmailTemplates() {
    const response = await this.api.get('/admin/email-templates');
    return response.data;
  }

  async getSmsTemplates() {
    const response = await this.api.get('/admin/sms-templates');
    return response.data;
  }

  async getWhatsappTemplates() {
    const response = await this.api.get('/admin/whatsapp-templates');
    return response.data;
  }

  // Recipient Search
  async searchPlayers(params) {
    const response = await this.api.get('/admin/players/search', { params });
    return response.data;
  }

  async searchClubs(params) {
    const response = await this.api.get('/admin/clubs/search', { params });
    return response.data;
  }

  async searchArbiters(params) {
    const response = await this.api.get('/admin/arbiters/search', { params });
    return response.data;
  }

  async searchTournaments(params) {
    const response = await this.api.get('/admin/tournaments/search', { params });
    return response.data;
  }

  // Bulk Messaging
  async sendBulkEmail(data) {
    const response = await this.api.post('/admin/send-email', data);
    return response.data;
  }

  async sendBulkSms(data) {
    const response = await this.api.post('/admin/send-sms', data);
    return response.data;
  }

  async sendBulkWhatsapp(data) {
    const response = await this.api.post('/admin/send-whatsapp', data);
    return response.data;
  }
}

export default new CommunicationAPI();
```

### 2. Template Selector Component

```jsx
// components/TemplateSelector.jsx
import React, { useState, useEffect } from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Button
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import communicationAPI from '../services/communicationApi';

const TemplateSelector = ({
  type, // 'email', 'sms', 'whatsapp'
  selectedTemplate,
  onTemplateSelect,
  onPreview
}) => {
  const [templates, setTemplates] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTemplates();
  }, [type]);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      let response;

      switch (type) {
        case 'email':
          response = await communicationAPI.getEmailTemplates();
          break;
        case 'sms':
          response = await communicationAPI.getSmsTemplates();
          break;
        case 'whatsapp':
          response = await communicationAPI.getWhatsappTemplates();
          break;
        default:
          return;
      }

      setTemplates(response.data.templates);
    } catch (error) {
      console.error('Error loading templates:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateSelect = (templateId) => {
    // Find the selected template
    let selectedTemplateData = null;
    Object.values(templates).forEach(categoryTemplates => {
      const found = categoryTemplates.find(t => t.id === templateId);
      if (found) selectedTemplateData = found;
    });

    onTemplateSelect(selectedTemplateData);
  };

  if (loading) {
    return <Typography>Loading templates...</Typography>;
  }

  return (
    <Box>
      <FormControl fullWidth margin="normal">
        <InputLabel>Select Template</InputLabel>
        <Select
          value={selectedTemplate?.id || ''}
          onChange={(e) => handleTemplateSelect(e.target.value)}
          label="Select Template"
        >
          <MenuItem value="">
            <em>None (Custom Content)</em>
          </MenuItem>
          {Object.entries(templates).map(([category, categoryTemplates]) => [
            <MenuItem key={`header-${category}`} disabled>
              <Typography variant="subtitle2" color="primary">
                {category.toUpperCase()}
              </Typography>
            </MenuItem>,
            ...categoryTemplates.map((template) => (
              <MenuItem key={template.id} value={template.id}>
                {template.name}
              </MenuItem>
            ))
          ])}
        </Select>
      </FormControl>

      {selectedTemplate && (
        <Box mt={2}>
          <Typography variant="h6">{selectedTemplate.name}</Typography>
          <Typography variant="body2" color="textSecondary" gutterBottom>
            {selectedTemplate.description}
          </Typography>

          <Box mt={1}>
            <Typography variant="subtitle2">Variables:</Typography>
            <Box display="flex" flexWrap="wrap" gap={1} mt={1}>
              {selectedTemplate.variables.map((variable) => (
                <Chip
                  key={variable}
                  label={variable}
                  size="small"
                  variant="outlined"
                />
              ))}
            </Box>
          </Box>

          {onPreview && (
            <Button
              variant="outlined"
              onClick={() => onPreview(selectedTemplate)}
              sx={{ mt: 2 }}
            >
              Preview Template
            </Button>
          )}
        </Box>
      )}
    </Box>
  );
};

export default TemplateSelector;
```

### 3. Recipient Search Component

```jsx
// components/RecipientSearch.jsx
import React, { useState, useEffect } from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  Typography,
  Chip,
  Grid
} from '@mui/material';
import communicationAPI from '../services/communicationApi';

const RecipientSearch = ({ onRecipientsChange }) => {
  const [recipientType, setRecipientType] = useState('players');
  const [searchParams, setSearchParams] = useState({});
  const [searchResults, setSearchResults] = useState([]);
  const [selectedRecipients, setSelectedRecipients] = useState([]);
  const [loading, setLoading] = useState(false);

  const searchMethods = {
    players: communicationAPI.searchPlayers,
    clubs: communicationAPI.searchClubs,
    arbiters: communicationAPI.searchArbiters,
    tournaments: communicationAPI.searchTournaments
  };

  const handleSearch = async () => {
    try {
      setLoading(true);
      const searchMethod = searchMethods[recipientType];
      const response = await searchMethod(searchParams);

      // Format results based on type
      let formattedResults = [];
      switch (recipientType) {
        case 'players':
          formattedResults = response.data.players.map(player => ({
            id: player.cbid,
            name: player.name,
            email: player.email,
            mobile: player.phoneNumber,
            type: 'player'
          }));
          break;
        case 'clubs':
          formattedResults = response.data.clubs.map(club => ({
            id: club.clubId,
            name: club.clubName,
            email: club.authorizedSignatoryEmail,
            mobile: club.authorizedSignatoryContactNumber,
            type: 'club'
          }));
          break;
        // Add other types as needed
      }

      setSearchResults(formattedResults);
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectRecipient = (recipient, isSelected) => {
    let newSelected;
    if (isSelected) {
      newSelected = [...selectedRecipients, recipient];
    } else {
      newSelected = selectedRecipients.filter(r => r.id !== recipient.id);
    }

    setSelectedRecipients(newSelected);
    onRecipientsChange(newSelected);
  };

  const handleSelectAll = (isSelected) => {
    if (isSelected) {
      setSelectedRecipients(searchResults);
      onRecipientsChange(searchResults);
    } else {
      setSelectedRecipients([]);
      onRecipientsChange([]);
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Select Recipients
      </Typography>

      <Grid container spacing={2} mb={2}>
        <Grid item xs={12} md={3}>
          <FormControl fullWidth>
            <InputLabel>Recipient Type</InputLabel>
            <Select
              value={recipientType}
              onChange={(e) => setRecipientType(e.target.value)}
              label="Recipient Type"
            >
              <MenuItem value="players">Players</MenuItem>
              <MenuItem value="clubs">Clubs</MenuItem>
              <MenuItem value="arbiters">Arbiters</MenuItem>
              <MenuItem value="tournaments">Tournaments</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={3}>
          <TextField
            fullWidth
            label="Name"
            value={searchParams.name || ''}
            onChange={(e) => setSearchParams({
              ...searchParams,
              name: e.target.value
            })}
          />
        </Grid>

        <Grid item xs={12} md={3}>
          <TextField
            fullWidth
            label="City"
            value={searchParams.city || ''}
            onChange={(e) => setSearchParams({
              ...searchParams,
              city: e.target.value
            })}
          />
        </Grid>

        <Grid item xs={12} md={3}>
          <Button
            variant="contained"
            onClick={handleSearch}
            disabled={loading}
            fullWidth
            sx={{ height: '56px' }}
          >
            {loading ? 'Searching...' : 'Search'}
          </Button>
        </Grid>
      </Grid>

      {selectedRecipients.length > 0 && (
        <Box mb={2}>
          <Typography variant="subtitle2">
            Selected Recipients ({selectedRecipients.length}):
          </Typography>
          <Box display="flex" flexWrap="wrap" gap={1} mt={1}>
            {selectedRecipients.slice(0, 10).map((recipient) => (
              <Chip
                key={recipient.id}
                label={recipient.name}
                onDelete={() => handleSelectRecipient(recipient, false)}
                size="small"
              />
            ))}
            {selectedRecipients.length > 10 && (
              <Chip
                label={`+${selectedRecipients.length - 10} more`}
                size="small"
                variant="outlined"
              />
            )}
          </Box>
        </Box>
      )}

      {searchResults.length > 0 && (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    checked={selectedRecipients.length === searchResults.length}
                    indeterminate={
                      selectedRecipients.length > 0 &&
                      selectedRecipients.length < searchResults.length
                    }
                    onChange={(e) => handleSelectAll(e.target.checked)}
                  />
                </TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Mobile</TableCell>
                <TableCell>Type</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {searchResults.map((recipient) => (
                <TableRow key={recipient.id}>
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={selectedRecipients.some(r => r.id === recipient.id)}
                      onChange={(e) => handleSelectRecipient(recipient, e.target.checked)}
                    />
                  </TableCell>
                  <TableCell>{recipient.name}</TableCell>
                  <TableCell>{recipient.email || 'N/A'}</TableCell>
                  <TableCell>{recipient.mobile || 'N/A'}</TableCell>
                  <TableCell>
                    <Chip label={recipient.type} size="small" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );
};

export default RecipientSearch;

### 4. Email Composer Component

```jsx
// components/EmailComposer.jsx
import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Paper,
  Divider
} from '@mui/material';
import { Editor } from '@tinymce/tinymce-react';
import TemplateSelector from './TemplateSelector';
import RecipientSearch from './RecipientSearch';
import communicationAPI from '../services/communicationApi';

const EmailComposer = () => {
  const [recipients, setRecipients] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [subject, setSubject] = useState('');
  const [customContent, setCustomContent] = useState('');
  const [templateData, setTemplateData] = useState({});
  const [sending, setSending] = useState(false);
  const [result, setResult] = useState(null);

  const handleSend = async () => {
    try {
      setSending(true);
      setResult(null);

      // Validate recipients have email
      const validRecipients = recipients.filter(r => r.email);
      if (validRecipients.length === 0) {
        throw new Error('No recipients with valid email addresses');
      }

      const payload = {
        recipients: validRecipients.map(r => ({
          email: r.email,
          name: r.name,
          id: r.id
        })),
        subject
      };

      if (selectedTemplate) {
        payload.templateName = selectedTemplate.id;
        payload.templateData = templateData;
      } else {
        payload.customContent = customContent;
      }

      const response = await communicationAPI.sendBulkEmail(payload);
      setResult(response.data);
    } catch (error) {
      setResult({
        error: error.message || 'Failed to send emails'
      });
    } finally {
      setSending(false);
    }
  };

  const renderTemplateDataInputs = () => {
    if (!selectedTemplate) return null;

    return (
      <Box mt={2}>
        <Typography variant="subtitle2" gutterBottom>
          Template Variables:
        </Typography>
        {selectedTemplate.variables.map((variable) => (
          <TextField
            key={variable}
            fullWidth
            label={variable}
            value={templateData[variable] || ''}
            onChange={(e) => setTemplateData({
              ...templateData,
              [variable]: e.target.value
            })}
            margin="normal"
            size="small"
          />
        ))}
      </Box>
    );
  };

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Send Bulk Email
      </Typography>

      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <RecipientSearch onRecipientsChange={setRecipients} />
      </Paper>

      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Email Content
        </Typography>

        <TextField
          fullWidth
          label="Subject"
          value={subject}
          onChange={(e) => setSubject(e.target.value)}
          margin="normal"
          required
        />

        <TemplateSelector
          type="email"
          selectedTemplate={selectedTemplate}
          onTemplateSelect={setSelectedTemplate}
        />

        {renderTemplateDataInputs()}

        {!selectedTemplate && (
          <Box mt={2}>
            <Typography variant="subtitle2" gutterBottom>
              Custom Content:
            </Typography>
            <Editor
              apiKey="your-tinymce-api-key"
              value={customContent}
              onEditorChange={setCustomContent}
              init={{
                height: 400,
                menubar: false,
                plugins: [
                  'advlist autolink lists link image charmap print preview anchor',
                  'searchreplace visualblocks code fullscreen',
                  'insertdatetime media table paste code help wordcount'
                ],
                toolbar:
                  'undo redo | formatselect | bold italic backcolor | \
                  alignleft aligncenter alignright alignjustify | \
                  bullist numlist outdent indent | removeformat | help'
              }}
            />
          </Box>
        )}
      </Paper>

      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="body2" color="textSecondary">
          {recipients.length} recipients selected
        </Typography>

        <Button
          variant="contained"
          onClick={handleSend}
          disabled={sending || recipients.length === 0 || !subject}
          startIcon={sending && <CircularProgress size={20} />}
        >
          {sending ? 'Sending...' : 'Send Email'}
        </Button>
      </Box>

      {result && (
        <Box mt={3}>
          {result.error ? (
            <Alert severity="error">{result.error}</Alert>
          ) : (
            <Alert severity="success">
              Successfully sent {result.successful} emails out of {result.totalRecipients} recipients.
              {result.failed > 0 && ` ${result.failed} failed.`}
            </Alert>
          )}
        </Box>
      )}
    </Box>
  );
};

export default EmailComposer;
```

### 5. SMS Composer Component

```jsx
// components/SmsComposer.jsx
import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Paper,
  FormHelperText
} from '@mui/material';
import TemplateSelector from './TemplateSelector';
import RecipientSearch from './RecipientSearch';
import communicationAPI from '../services/communicationApi';

const SmsComposer = () => {
  const [recipients, setRecipients] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [variables, setVariables] = useState({});
  const [sending, setSending] = useState(false);
  const [result, setResult] = useState(null);

  const handleSend = async () => {
    try {
      setSending(true);
      setResult(null);

      // Validate recipients have mobile
      const validRecipients = recipients.filter(r => r.mobile);
      if (validRecipients.length === 0) {
        throw new Error('No recipients with valid mobile numbers');
      }

      if (!selectedTemplate) {
        throw new Error('Please select a template for SMS');
      }

      const payload = {
        recipients: validRecipients.map(r => ({
          mobile: r.mobile,
          name: r.name,
          id: r.id
        })),
        templateId: selectedTemplate.templateId,
        variables
      };

      const response = await communicationAPI.sendBulkSms(payload);
      setResult(response.data);
    } catch (error) {
      setResult({
        error: error.message || 'Failed to send SMS'
      });
    } finally {
      setSending(false);
    }
  };

  const renderVariableInputs = () => {
    if (!selectedTemplate) return null;

    return (
      <Box mt={2}>
        <Typography variant="subtitle2" gutterBottom>
          SMS Variables:
        </Typography>
        {selectedTemplate.variables.map((variable) => (
          <TextField
            key={variable}
            fullWidth
            label={variable}
            value={variables[variable] || ''}
            onChange={(e) => setVariables({
              ...variables,
              [variable]: e.target.value
            })}
            margin="normal"
            size="small"
            helperText={`This will replace ${variable} in the SMS template`}
          />
        ))}
      </Box>
    );
  };

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Send Bulk SMS
      </Typography>

      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <RecipientSearch onRecipientsChange={setRecipients} />
      </Paper>

      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          SMS Content
        </Typography>

        <Alert severity="info" sx={{ mb: 2 }}>
          SMS messages can only use predefined templates. Custom content is not allowed.
        </Alert>

        <TemplateSelector
          type="sms"
          selectedTemplate={selectedTemplate}
          onTemplateSelect={setSelectedTemplate}
        />

        {renderVariableInputs()}

        {selectedTemplate && (
          <Box mt={2}>
            <Typography variant="subtitle2">
              Character Limit: {selectedTemplate.maxLength} characters
            </Typography>
            <FormHelperText>
              SMS messages exceeding 160 characters will be sent as multiple SMS.
            </FormHelperText>
          </Box>
        )}
      </Paper>

      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="body2" color="textSecondary">
          {recipients.filter(r => r.mobile).length} recipients with mobile numbers
        </Typography>

        <Button
          variant="contained"
          onClick={handleSend}
          disabled={sending || recipients.length === 0 || !selectedTemplate}
          startIcon={sending && <CircularProgress size={20} />}
        >
          {sending ? 'Sending...' : 'Send SMS'}
        </Button>
      </Box>

      {result && (
        <Box mt={3}>
          {result.error ? (
            <Alert severity="error">{result.error}</Alert>
          ) : (
            <Alert severity="success">
              Successfully sent {result.successful} SMS out of {result.totalRecipients} recipients.
              {result.failed > 0 && ` ${result.failed} failed.`}
            </Alert>
          )}
        </Box>
      )}
    </Box>
  );
};

export default SmsComposer;
```

### 6. WhatsApp Composer Component

```jsx
// components/WhatsappComposer.jsx
import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Paper,
  FormHelperText
} from '@mui/material';
import TemplateSelector from './TemplateSelector';
import RecipientSearch from './RecipientSearch';
import communicationAPI from '../services/communicationApi';

const WhatsappComposer = () => {
  const [recipients, setRecipients] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [variables, setVariables] = useState({});
  const [sending, setSending] = useState(false);
  const [result, setResult] = useState(null);

  const handleSend = async () => {
    try {
      setSending(true);
      setResult(null);

      // Validate recipients have mobile
      const validRecipients = recipients.filter(r => r.mobile);
      if (validRecipients.length === 0) {
        throw new Error('No recipients with valid mobile numbers');
      }

      if (!selectedTemplate) {
        throw new Error('Please select a template for WhatsApp');
      }

      const payload = {
        recipients: validRecipients.map(r => ({
          mobile: r.mobile,
          name: r.name,
          id: r.id
        })),
        templateId: selectedTemplate.id,
        variables
      };

      const response = await communicationAPI.sendBulkWhatsapp(payload);
      setResult(response.data);
    } catch (error) {
      setResult({
        error: error.message || 'Failed to send WhatsApp messages'
      });
    } finally {
      setSending(false);
    }
  };

  const renderVariableInputs = () => {
    if (!selectedTemplate) return null;

    return (
      <Box mt={2}>
        <Typography variant="subtitle2" gutterBottom>
          WhatsApp Variables:
        </Typography>
        {selectedTemplate.variables.map((variable) => (
          <TextField
            key={variable}
            fullWidth
            label={variable}
            value={variables[variable] || ''}
            onChange={(e) => setVariables({
              ...variables,
              [variable]: e.target.value
            })}
            margin="normal"
            size="small"
            helperText={`This will replace {${variable}} in the WhatsApp template`}
          />
        ))}
      </Box>
    );
  };

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Send Bulk WhatsApp
      </Typography>

      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <RecipientSearch onRecipientsChange={setRecipients} />
      </Paper>

      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          WhatsApp Content
        </Typography>

        <Alert severity="info" sx={{ mb: 2 }}>
          WhatsApp messages can only use predefined templates. Custom content is not allowed.
        </Alert>

        <Alert severity="warning" sx={{ mb: 2 }}>
          Note: WhatsApp integration is currently in development. This is a simulation.
        </Alert>

        <TemplateSelector
          type="whatsapp"
          selectedTemplate={selectedTemplate}
          onTemplateSelect={setSelectedTemplate}
        />

        {renderVariableInputs()}

        {selectedTemplate && (
          <Box mt={2}>
            <Typography variant="subtitle2">
              Character Limit: {selectedTemplate.maxLength} characters
            </Typography>
            <FormHelperText>
              WhatsApp messages can be much longer than SMS.
            </FormHelperText>
          </Box>
        )}
      </Paper>

      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="body2" color="textSecondary">
          {recipients.filter(r => r.mobile).length} recipients with mobile numbers
        </Typography>

        <Button
          variant="contained"
          onClick={handleSend}
          disabled={sending || recipients.length === 0 || !selectedTemplate}
          startIcon={sending && <CircularProgress size={20} />}
        >
          {sending ? 'Sending...' : 'Send WhatsApp'}
        </Button>
      </Box>

      {result && (
        <Box mt={3}>
          {result.error ? (
            <Alert severity="error">{result.error}</Alert>
          ) : (
            <Alert severity="success">
              Successfully sent {result.successful} WhatsApp messages out of {result.totalRecipients} recipients.
              {result.failed > 0 && ` ${result.failed} failed.`}
              {result.note && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {result.note}
                </Typography>
              )}
            </Alert>
          )}
        </Box>
      )}
    </Box>
  );
};

export default WhatsappComposer;
```

### 7. Main Messaging Dashboard

```jsx
// components/MessagingDashboard.jsx
import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Icon
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import EmailIcon from '@mui/icons-material/Email';
import SmsIcon from '@mui/icons-material/Sms';
import WhatsAppIcon from '@mui/icons-material/WhatsApp';

const MessagingDashboard = () => {
  const navigate = useNavigate();

  const messagingOptions = [
    {
      title: 'Email',
      description: 'Send bulk emails with custom content or templates',
      icon: EmailIcon,
      color: '#1976d2',
      path: '/admin/messaging/email',
      features: ['Custom Content', 'Templates', 'Rich Text Editor', 'Attachments']
    },
    {
      title: 'SMS',
      description: 'Send bulk SMS using predefined templates',
      icon: SmsIcon,
      color: '#388e3c',
      path: '/admin/messaging/sms',
      features: ['Predefined Templates', '160 Character Limit', 'Instant Delivery']
    },
    {
      title: 'WhatsApp',
      description: 'Send bulk WhatsApp messages using templates',
      icon: WhatsAppIcon,
      color: '#25d366',
      path: '/admin/messaging/whatsapp',
      features: ['Template Messages', 'Rich Media', 'High Engagement']
    }
  ];

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Messaging Center
      </Typography>
      <Typography variant="body1" color="textSecondary" paragraph>
        Send bulk communications to players, clubs, arbiters, and tournament participants.
      </Typography>

      <Grid container spacing={3}>
        {messagingOptions.map((option) => (
          <Grid item xs={12} md={4} key={option.title}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                '&:hover': {
                  boxShadow: 6,
                  transform: 'translateY(-2px)',
                  transition: 'all 0.3s ease-in-out'
                }
              }}
            >
              <CardContent sx={{ flexGrow: 1 }}>
                <Box display="flex" alignItems="center" mb={2}>
                  <Icon
                    component={option.icon}
                    sx={{
                      fontSize: 40,
                      color: option.color,
                      mr: 2
                    }}
                  />
                  <Typography variant="h5" component="h2">
                    {option.title}
                  </Typography>
                </Box>

                <Typography variant="body2" color="textSecondary" paragraph>
                  {option.description}
                </Typography>

                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Features:
                  </Typography>
                  {option.features.map((feature) => (
                    <Typography
                      key={feature}
                      variant="body2"
                      color="textSecondary"
                      sx={{ display: 'block', ml: 1 }}
                    >
                      • {feature}
                    </Typography>
                  ))}
                </Box>
              </CardContent>

              <CardActions>
                <Button
                  variant="contained"
                  fullWidth
                  onClick={() => navigate(option.path)}
                  sx={{
                    backgroundColor: option.color,
                    '&:hover': {
                      backgroundColor: option.color,
                      filter: 'brightness(0.9)'
                    }
                  }}
                >
                  Send {option.title}
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default MessagingDashboard;
```

### 8. React Router Setup

```jsx
// App.jsx or your main routing file
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import MessagingDashboard from './components/MessagingDashboard';
import EmailComposer from './components/EmailComposer';
import SmsComposer from './components/SmsComposer';
import WhatsappComposer from './components/WhatsappComposer';

function App() {
  return (
    <Router>
      <Routes>
        {/* Other routes */}

        {/* Messaging Routes */}
        <Route path="/admin/messaging" element={<MessagingDashboard />} />
        <Route path="/admin/messaging/email" element={<EmailComposer />} />
        <Route path="/admin/messaging/sms" element={<SmsComposer />} />
        <Route path="/admin/messaging/whatsapp" element={<WhatsappComposer />} />

        {/* Other routes */}
      </Routes>
    </Router>
  );
}

export default App;
```

### 9. Usage Examples

```jsx
// Example: Using the API service directly
import communicationAPI from './services/communicationApi';

// Get email templates
const emailTemplates = await communicationAPI.getEmailTemplates();
console.log(emailTemplates.data.templates);

// Search for players
const players = await communicationAPI.searchPlayers({
  playerName: 'john',
  city: 'mumbai',
  page: 1,
  limit: 10
});

// Send bulk email
const emailResult = await communicationAPI.sendBulkEmail({
  recipients: [
    { email: '<EMAIL>', name: 'John Doe', id: 'player123' }
  ],
  subject: 'Tournament Registration',
  templateName: 'tournament-registration',
  templateData: {
    tournamentName: 'National Championship',
    startDate: '2024-12-15'
  }
});

// Send bulk SMS
const smsResult = await communicationAPI.sendBulkSms({
  recipients: [
    { mobile: '919876543210', name: 'John Doe', id: 'player123' }
  ],
  templateId: 'registration_confirmation_template_id',
  variables: {
    VAR1: 'John Doe',
    VAR2: 'National Championship'
  }
});
```

## Installation Requirements

To use these components, you'll need to install the following dependencies:

```bash
npm install @mui/material @emotion/react @emotion/styled
npm install @mui/icons-material
npm install @tinymce/tinymce-react
npm install axios
npm install react-router-dom
```

## Integration Steps

1. **Copy the API service** (`communicationApi.js`) to your services directory
2. **Add the components** to your components directory
3. **Update your routing** to include the messaging routes
4. **Configure environment variables** for your API base URL
5. **Add the messaging dashboard** to your admin navigation menu

## Customization

- **Styling**: All components use Material-UI and can be customized with themes
- **Templates**: Add more template types by extending the template selector
- **Validation**: Add custom validation rules for recipients and content
- **Permissions**: Add role-based access control for different messaging types
- **Analytics**: Add tracking for message delivery and engagement metrics
