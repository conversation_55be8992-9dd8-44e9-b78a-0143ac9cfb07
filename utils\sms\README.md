# SMS Service

This module provides SMS functionality for the Chess Brigade application using MSG91 API.

## Configuration

The SMS service requires the following environment variables:

```
MSG91_AUTH_KEY=your_auth_key
MSG91_SENDER_ID=your_sender_id (default: CHSBRD)
```

## Usage

### Basic Usage

```javascript
const { sendSMS } = require('./utils/sms');

// Send a simple SMS
sendSMS({
  templateId: 'your_template_id',
  mobile: '919XXXXXXXXX',
  variables: {
    VAR1: 'Value 1',
    VAR2: 'Value 2'
  }
})
.then(response => console.log('SMS sent:', response))
.catch(error => console.error('Error sending SMS:', error));
```

### Using the SMS Service

```javascript
const smsService = require('./utils/sms/smsService');

// Send an OTP SMS
smsService.sendOtpSMS({
  mobile: '919XXXXXXXXX',
  otp: '123456',
  expiryMinutes: 5
})
.then(response => console.log('OTP SMS sent:', response))
.catch(error => console.error('Error sending OTP SMS:', error));

// Send a registration confirmation SMS
smsService.sendRegistrationConfirmationSMS(
  registration, // Registration object
  player,       // Player object
  tournament    // Tournament object
)
.then(response => console.log('Registration SMS sent:', response))
.catch(error => console.error('Error sending registration SMS:', error));
```

## Available Templates

The SMS service includes the following predefined templates:

1. **Welcome SMS** - `sendWelcomeSMS(user)`
2. **OTP SMS** - `sendOtpSMS({ mobile, otp, expiryMinutes })`
3. **Registration Confirmation** - `sendRegistrationConfirmationSMS(registration, player, tournament)`
4. **Payment Confirmation** - `sendPaymentConfirmationSMS(payment, player, tournament)`
5. **Password Reset** - `sendPasswordResetSMS(user, resetToken)`
6. **Club Invitation** - `sendClubInviteSMS(invitation)`
7. **Custom SMS** - `sendCustomSMS({ templateId, mobile, variables, shortUrl, shortUrlExpiry })`

## MSG91 Template Setup

For each SMS type, you need to create a template in the MSG91 dashboard and replace the placeholder template IDs in the `smsService.js` file.

Example template for OTP:
```
Your OTP for Chess Brigade is {{VAR1}}. It is valid for {{VAR2}} minutes. Do not share this OTP with anyone.
```

## Verifying Configuration

To verify your SMS configuration, run:

```
npm run verify:sms
```

This will check if your MSG91 credentials are properly configured.
