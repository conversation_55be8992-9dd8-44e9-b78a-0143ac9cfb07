/**
 * Sends a formatted API response
 * @param {import('express').Response} res - Express response object
 * @param {number} statusCode - HTTP status code
 * @param {object} response - Response data
 */
const sendResponse = (res, statusCode, response) => {
  res.status(statusCode).json(response);
  return;
};

/**
 * Handles errors and sends appropriate response
 * @param {import('express').Response} res - Express response object
 * @param {unknown} error - Error object
 */
const handleError = (res, error) => {
  console.error("Error:", error);

  sendResponse(res, 500, {
    success: false,
    error: "Internal Server Error",
  });
  return;
};

module.exports = { sendResponse, handleError };
