const router = require("express").Router({ mergeParams: true });
const { Country, State, City } = require("country-state-city");
const District = require("../utils/getDistricts/district");
const { sendResponse } = require("../utils/apiResponse");

router.get("/countries", (req, res) => {
  res.set("Cache-Control", "public, max-age=86400");
  sendResponse(res, 200, {
    success: true,
    data: Country.getAllCountries(),
  });
});

router.get("/states/:countryCode", (req, res) => {
  const { countryCode } = req.params;
  res.set("Cache-Control", "public, max-age=86400");
  sendResponse(res, 200, {
    success: true,
    data: State.getStatesOfCountry(countryCode),
  });
});

router.get("/districts/:stateCode", (req, res) => {
  const { stateCode } = req.params;
  res.set("Cache-Control", "public, max-age=86400"); // Cache for 1 day
  sendResponse(res, 200, {
    success: true,
    data: District.getDistrictsOfState(stateCode),
  });
});

router.get("/cities/:countryCode/:stateCode", (req, res) => {
  const { countryCode, stateCode } = req.params;
  res.set("Cache-Control", "public, max-age=86400"); // Cache for 1 day
  sendResponse(res, 200, {
    success: true,
    data: City.getCitiesOfState(countryCode, stateCode),
  });
});

module.exports = router;
