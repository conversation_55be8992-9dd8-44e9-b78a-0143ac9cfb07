#!/usr/bin/env node

/**
 * Load Test Data Cleanup Script
 * 
 * This script removes all load testing data created by the load test seed:
 * - All users with email pattern '<EMAIL>'
 * - All associated club details, player details, arbiter details
 * - All tournaments with title pattern 'loadtest-%'
 * - All registrations and payments associated with load test data
 * 
 * Usage: node utils/seed/cleanupLoadTestData.js
 */

const { sequelize, models } = require('../../config/db');

async function cleanupLoadTestData() {
    try {
        console.log('🧹 Starting Load Test Data Cleanup...');
        console.log('⚠️  This will permanently delete all load test data!');
        console.log('');

        // Test database connection
        await sequelize.authenticate();
        console.log('✅ Database connection established successfully.');

        const startTime = Date.now();

        // Get load test users first
        const loadTestUsers = await models.User.findAll({
            where: {
                email: {
                    [require('sequelize').Op.like]: '<EMAIL>'
                }
            },
            attributes: ['id', 'email', 'role']
        });

        console.log(`Found ${loadTestUsers.length} load test users to clean up`);

        if (loadTestUsers.length === 0) {
            console.log('No load test data found. Nothing to clean up.');
            return;
        }

        const userIds = loadTestUsers.map(user => user.id);

        // Get load test tournaments
        const loadTestTournaments = await models.Tournament.findAll({
            where: {
                title: {
                    [require('sequelize').Op.like]: 'loadtest-%'
                }
            },
            attributes: ['id']
        });

        const tournamentIds = loadTestTournaments.map(tournament => tournament.id);
        console.log(`Found ${tournamentIds.length} load test tournaments to clean up`);

        // Start cleanup in correct order (due to foreign key constraints)
        
        // 1. Delete payments first
        console.log('🗑️  Deleting payments...');
        const deletedPayments = await models.Payment.destroy({
            where: {
                [require('sequelize').Op.or]: [
                    { userId: { [require('sequelize').Op.in]: userIds } },
                    { tournamentId: { [require('sequelize').Op.in]: tournamentIds } },
                    { paymentTransactionId: { [require('sequelize').Op.like]: 'LT-%' } }
                ]
            }
        });
        console.log(`   Deleted ${deletedPayments} payments`);

        // 2. Delete registrations
        console.log('🗑️  Deleting registrations...');
        const deletedRegistrations = await models.Registration.destroy({
            where: {
                [require('sequelize').Op.or]: [
                    { playerId: { [require('sequelize').Op.in]: userIds } },
                    { tournamentId: { [require('sequelize').Op.in]: tournamentIds } },
                    { regId: { [require('sequelize').Op.like]: 'LTR%' } }
                ]
            }
        });
        console.log(`   Deleted ${deletedRegistrations} registrations`);

        // 3. Delete bulk registrations
        console.log('🗑️  Deleting bulk registrations...');
        const deletedBulkRegistrations = await models.BulkRegistration.destroy({
            where: {
                [require('sequelize').Op.or]: [
                    { tournamentId: { [require('sequelize').Op.in]: tournamentIds } },
                    { bulkRegistrationId: { [require('sequelize').Op.like]: 'ltbr-%' } }
                ]
            }
        });
        console.log(`   Deleted ${deletedBulkRegistrations} bulk registrations`);

        // 4. Delete tournaments
        console.log('🗑️  Deleting tournaments...');
        const deletedTournaments = await models.Tournament.destroy({
            where: {
                title: {
                    [require('sequelize').Op.like]: 'loadtest-%'
                }
            }
        });
        console.log(`   Deleted ${deletedTournaments} tournaments`);

        // 5. Delete club details
        console.log('🗑️  Deleting club details...');
        const deletedClubDetails = await models.ClubDetail.destroy({
            where: {
                userId: { [require('sequelize').Op.in]: userIds }
            }
        });
        console.log(`   Deleted ${deletedClubDetails} club details`);

        // 6. Delete player details
        console.log('🗑️  Deleting player details...');
        const deletedPlayerDetails = await models.PlayerDetail.destroy({
            where: {
                userId: { [require('sequelize').Op.in]: userIds }
            }
        });
        console.log(`   Deleted ${deletedPlayerDetails} player details`);

        // 7. Delete arbiter details
        console.log('🗑️  Deleting arbiter details...');
        const deletedArbiterDetails = await models.ArbiterDetails.destroy({
            where: {
                userId: { [require('sequelize').Op.in]: userIds }
            }
        });
        console.log(`   Deleted ${deletedArbiterDetails} arbiter details`);

        // 8. Finally delete users
        console.log('🗑️  Deleting users...');
        const deletedUsers = await models.User.destroy({
            where: {
                email: {
                    [require('sequelize').Op.like]: '<EMAIL>'
                }
            }
        });
        console.log(`   Deleted ${deletedUsers} users`);

        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);

        console.log('');
        console.log('🎉 Load Test Data Cleanup Completed Successfully!');
        console.log(`⏱️  Total time taken: ${duration} seconds`);
        console.log('');
        console.log('📊 Summary of deleted data:');
        console.log(`  👥 Users: ${deletedUsers}`);
        console.log(`  🏢 Club Details: ${deletedClubDetails}`);
        console.log(`  🎯 Player Details: ${deletedPlayerDetails}`);
        console.log(`  ⚖️  Arbiter Details: ${deletedArbiterDetails}`);
        console.log(`  🏆 Tournaments: ${deletedTournaments}`);
        console.log(`  📝 Registrations: ${deletedRegistrations}`);
        console.log(`  📋 Bulk Registrations: ${deletedBulkRegistrations}`);
        console.log(`  💳 Payments: ${deletedPayments}`);
        console.log('');
        console.log('✨ Database is now clean of load test data!');

    } catch (error) {
        console.error('❌ Error during load test data cleanup:', error);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    } finally {
        // Close database connection
        await sequelize.close();
        console.log('🔌 Database connection closed.');
    }
}

// Handle process termination gracefully
process.on('SIGINT', async () => {
    console.log('\n⚠️  Received SIGINT. Closing database connection...');
    await sequelize.close();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n⚠️  Received SIGTERM. Closing database connection...');
    await sequelize.close();
    process.exit(0);
});

// Run the script if called directly
if (require.main === module) {
    cleanupLoadTestData();
}

module.exports = cleanupLoadTestData;
