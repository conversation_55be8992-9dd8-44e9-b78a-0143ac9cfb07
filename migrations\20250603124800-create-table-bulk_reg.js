'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('bulk_registrations', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        allowNull: false,
        primaryKey: true,
      },
      bulk_registration_id: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
      },
      tournament_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'tournament',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      registered_by: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'club_details',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      registration_status: {
        type: Sequelize.ENUM('pending', 'registered', 'rejected'),
        allowNull: false,
        defaultValue: 'pending',
      },
      player_list: {
        type: Sequelize.JSONB,
        allowNull: false,
      },
      total_amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
      },
      players_count: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      remarks: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('NOW'),
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('NOW'),
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('bulk_registrations');
  }
};
