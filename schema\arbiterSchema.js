const z = require("zod");

const arbiterDetailSchema = z.object({
  title: z.string().max(50).optional().default("Untitled"),
  profileUrl: z.string().url().optional(),
  alternateContact: z.string().max(15).optional(),
  fideRating: z.string().optional().default("Unrated"),
  fideId: z.string().max(20).optional(),
  aicfId: z.string().max(20).optional(),
  stateId: z.string().max(20).optional(),
  officialId: z.string().max(20).optional(),
  association: z.string().max(100).optional(),
  club: z.string().max(100).optional(),
  country: z.string().max(50).optional(),
  countryCode: z.string().max(4).optional(),
  state: z.string().max(50).optional(),
  district: z.string().max(50).optional(),
  city: z.string().max(50).optional(),
  pincode: z.string().max(10).optional(),
});

const updateDetailsSchema = z.object({
  phoneChanged: z.coerce.boolean().optional().default(false),
  otp: z.string().length(6).optional(),
  phoneNumber: z
    .string()
    .regex(/^(0\d{9,14}|\+?[1-9]\d{9,14})$/, {
      message: "Phone number must be 10 digits",
    })
    .optional(),
  firstName: z.string().min(1).max(50).optional(),
  lastName: z.string().min(1).max(50).optional(),
  name: z.string().min(1).max(110).optional(),
  title: z.string().max(50).optional().default("Untitled"),
  profileUrl: z.string().url().optional(),
  alternateContact: z.string().max(15).optional(),
  fideRating: z.string().optional().default("Unrated"),
  fideId: z.string().max(20).optional(),
  aicfId: z.string().max(20).optional(),
  stateId: z.string().max(20).optional(),
  officialId: z.string().max(20).optional(),
  association: z.string().max(100).optional(),
  club: z.string().max(100).optional(),
  country: z.string().max(50).default("India"),
  countryCode: z.string().max(4).default("IN"),
  state: z.string().max(50).default("Tamilnadu"),
  district: z.string().max(50).default("Chennai"),
  city: z.string().max(50).default("Chennai"),
  pincode: z.string().max(10),
});

module.exports = { arbiterDetailSchema, updateDetailsSchema };
