const express = require('express');
const router = express.Router();

const {
    createAdvertisement,
    getAllAdvertisements,
    getAdvertisementById,
    updateAdvertisement,
    deleteAdvertisement,
    updateIsActive,
} = require('../../controllers/admin/Advertisement');
const { uploadFactory, handleUploadError } = require('../../utils/s3');

// Create Advertisement
router.post('/', uploadFactory.advertisement.image(),handleUploadError,createAdvertisement);

// Get All Advertisements
router.get('/', getAllAdvertisements);

// Get Advertisement by ID
router.get('/:id', getAdvertisementById);

// Update Advertisement
router.put('/:id', updateAdvertisement);

// Update Advertisement
router.put('/', updateIsActive);

// Delete Advertisement
router.delete('/:id', deleteAdvertisement);

module.exports = router;
