'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('certificates', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        allowNull: false,
        primaryKey: true,
      },
      certificate_id: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      tournament_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'tournament',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      player_name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      total_points: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      rank: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      age_category: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      gender_category: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      round_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      organization: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      powered_by: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      sponsor_name: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      subtitle: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      tournament_date: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      venue: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      template_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
      },
      tournament_title: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('NOW'),
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('NOW'),
      },
    });

    // Create indexes for better query performance
    await queryInterface.addIndex('certificates', ['tournament_id'], {
      name: 'certificates_tournament_id_index',
    });

    await queryInterface.addIndex('certificates', ['user_id'], {
      name: 'certificates_user_id_index',
    });

    await queryInterface.addIndex('certificates', ['tournament_id', 'user_id'], {
      name: 'certificates_tournament_user_index',
    });

    await queryInterface.addIndex('certificates', ['rank'], {
      name: 'certificates_rank_index',
    });

    await queryInterface.addIndex('certificates', ['template_id'], {
      name: 'certificates_template_id_index',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('certificates');
  }
};