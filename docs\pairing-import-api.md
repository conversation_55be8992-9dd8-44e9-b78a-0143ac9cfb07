# Pairing Import API Documentation

This document describes the API endpoints for the Pairing Import feature, which allows you to upload Excel files containing pairing data and import them into the database.

## Base URL

All endpoints are prefixed with `/api/pairing-import`.

## Authentication

All endpoints require authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your_token>
```

## Endpoints

### Upload and Process Excel File

**Endpoint:** `POST /upload`

**Description:** Upload an Excel file and process it to extract pairing data into the database.

**Request:**
- Content-Type: `multipart/form-data`

**Form Fields:**
- `file` (required): The Excel file to upload
- `tournament_id` (required): UUID of the tournament to associate the pairings with
- `round_id` (optional): Round number (default: 1)
- `category` (optional): Category of the pairings (default: 'Default')
- `deleteAfterProcessing` (optional): Set to 'true' to delete the file after processing

**Example Request:**
```bash
curl -X POST \
  -H "Authorization: Bearer <your_token>" \
  -F "file=@path/to/your/file.xlsx" \
  -F "tournament_id=123e4567-e89b-12d3-a456-************" \
  -F "round_id=1" \
  -F "category=Open" \
  -F "deleteAfterProcessing=true" \
  http://localhost:3000/api/pairing-import/upload
```

**Response:**
```json
{
  "success": true,
  "message": "File processed successfully",
  "data": {
    "recordsProcessed": 20,
    "filename": "pairings.xlsx",
    "tournament_id": "123e4567-e89b-12d3-a456-************",
    "round_id": 1,
    "category": "Open"
  }
}
```

### Get All Pairings

**Endpoint:** `GET /`

**Description:** Get all pairings, with optional filtering by tournament ID, round ID, and category.

**Query Parameters:**
- `tournament_id` (optional): Filter by tournament ID
- `round_id` (optional): Filter by round ID
- `category` (optional): Filter by category
- `limit` (optional): Number of records to return (default: 100)
- `offset` (optional): Number of records to skip (default: 0)

**Example Request:**
```bash
curl -X GET \
  -H "Authorization: Bearer <your_token>" \
  "http://localhost:3000/api/pairing-import?tournament_id=123e4567-e89b-12d3-a456-************&round_id=1&category=Open&limit=10&offset=0"
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "tournament_id": "123e4567-e89b-12d3-a456-************",
      "round_id": 1,
      "category": "Open",
      "board_no": 1,
      "white_player_name": "John Doe",
      "white_player_ranking": 1,
      "white_player_fide_rating": 2200,
      "white_player_points": 3.5,
      "black_player_name": "Jane Smith",
      "black_player_ranking": 2,
      "black_player_fide_rating": 2150,
      "black_player_points": 3.0,
      "result": "white",
      "created_at": "2023-05-01T12:00:00.000Z",
      "updated_at": "2023-05-01T12:00:00.000Z"
    },
    // More records...
  ],
  "count": 20,
  "limit": 10,
  "offset": 0
}
```

### Get Pairing by ID

**Endpoint:** `GET /:id`

**Description:** Get a single pairing by ID.

**URL Parameters:**
- `id` (required): ID of the pairing

**Example Request:**
```bash
curl -X GET \
  -H "Authorization: Bearer <your_token>" \
  "http://localhost:3000/api/pairing-import/1"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "tournament_id": "123e4567-e89b-12d3-a456-************",
    "round_id": 1,
    "category": "Open",
    "board_no": 1,
    "white_player_name": "John Doe",
    "white_player_ranking": 1,
    "white_player_fide_rating": 2200,
    "white_player_points": 3.5,
    "black_player_name": "Jane Smith",
    "black_player_ranking": 2,
    "black_player_fide_rating": 2150,
    "black_player_points": 3.0,
    "result": "white",
    "created_at": "2023-05-01T12:00:00.000Z",
    "updated_at": "2023-05-01T12:00:00.000Z"
  }
}
```

### Delete Pairing by ID

**Endpoint:** `DELETE /:id`

**Description:** Delete a single pairing by ID.

**URL Parameters:**
- `id` (required): ID of the pairing

**Example Request:**
```bash
curl -X DELETE \
  -H "Authorization: Bearer <your_token>" \
  "http://localhost:3000/api/pairing-import/1"
```

**Response:**
```json
{
  "success": true,
  "message": "Pairing deleted successfully"
}
```

### Delete All Pairings for a Tournament

**Endpoint:** `DELETE /tournament/:tournament_id`

**Description:** Delete all pairings for a specific tournament.

**URL Parameters:**
- `tournament_id` (required): UUID of the tournament

**Example Request:**
```bash
curl -X DELETE \
  -H "Authorization: Bearer <your_token>" \
  "http://localhost:3000/api/pairing-import/tournament/123e4567-e89b-12d3-a456-************"
```

**Response:**
```json
{
  "success": true,
  "message": "20 pairings deleted successfully"
}
```

### Delete All Pairings for a Tournament and Round

**Endpoint:** `DELETE /tournament/:tournament_id/round/:round_id`

**Description:** Delete all pairings for a specific tournament and round.

**URL Parameters:**
- `tournament_id` (required): UUID of the tournament
- `round_id` (required): Round number

**Example Request:**
```bash
curl -X DELETE \
  -H "Authorization: Bearer <your_token>" \
  "http://localhost:3000/api/pairing-import/tournament/123e4567-e89b-12d3-a456-************/round/1"
```

**Response:**
```json
{
  "success": true,
  "message": "10 pairings deleted successfully"
}
```

## Error Responses

All endpoints return a standard error format:

```json
{
  "success": false,
  "message": "Error message describing what went wrong"
}
```

Common error status codes:
- `400`: Bad Request (e.g., missing required fields)
- `401`: Unauthorized (missing or invalid token)
- `404`: Not Found (resource not found)
- `500`: Internal Server Error (server-side error)

## Excel File Format

The API supports two types of Excel file formats for pairings:

### Standard Format

The Excel file can contain pairing data with columns that have unique headers:

- Board number: 'Board', 'Board No', 'Table', 'Table No', 'Bo.'
- White player: 'White', 'White Player', 'White Name'
- White player rating: 'White Rating', 'White Rtg', 'White FIDE'
- White player ranking: 'White Rank', 'White Ranking'
- White player points: 'White Pts', 'White Points'
- Black player: 'Black', 'Black Player', 'Black Name'
- Black player rating: 'Black Rating', 'Black Rtg', 'Black FIDE'
- Black player ranking: 'Black Rank', 'Black Ranking'
- Black player points: 'Black Pts', 'Black Points'
- Result: 'Result', 'Score'

### Special Format with Repeating Headers

The API also supports a specific format with repeating headers in this order:
```
Bo. | White | Rtg | pts | result | pts | Black | Rtg
```

In this format:
- 'Bo.' is the board number
- 'White' is the white player's name
- First 'Rtg' is the white player's rating
- First 'pts' is the white player's points
- 'result' is the game result (1-0, 0-1, 0-0)
- Second 'pts' is the black player's points
- 'Black' is the black player's name
- Second 'Rtg' is the black player's rating

The system automatically detects this special format and uses position-based mapping instead of header-based mapping.

### Result Formats

For the standard format, the result field can be in various formats:
- '1-0', '1:0', 'white', 'w' for white win
- '0-1', '0:1', 'black', 'b' for black win
- '½-½', '1/2-1/2', '0.5-0.5', 'draw', 'd', '=', '½:½', '0.5:0.5' for draw

For the special format, the result is stored exactly as it appears in the Excel file:
- '1-0' for white win
- '0-1' for black win
- '0-0' for draw
