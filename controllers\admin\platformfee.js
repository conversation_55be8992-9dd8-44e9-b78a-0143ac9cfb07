const { PlatformFee } = require("../../config/db").models;
const { sendResponse, handleError } = require("../../utils/apiResponse");

const updatePlatformfee = async (req, res) => {
  try {
    const { fee_percentage } = req.body;
    const platform = await PlatformFee.updateFeePercentage(fee_percentage);
    return sendResponse(res, 200, {
      success: true,
      data: platform,
    });
  } catch (error) {
    return handleError(res, error);
  }
};

const getPlatformfee = async (req, res) => {
  try {
    const platform = await PlatformFee.findOne({
      where: { is_active: true },
    
    });

    return sendResponse(res, 200, {
      success: true,
      data: platform,
    });
  } catch (error) {
    return handleError(res, error);
  }
};

module.exports = {
  updatePlatformfee,
  getPlatformfee,
};
