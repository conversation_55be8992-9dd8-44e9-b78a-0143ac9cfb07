/**
 * Test script for the Excel extractor
 * 
 * This script tests the Excel extractor without saving to the database.
 * It's useful for testing the extraction logic without affecting the database.
 * 
 * Usage:
 *   node scripts/test-extractor.js [file.xlsx]
 */

const { processExcelFile, analyzeWorksheet } = require('./excel-extractor');
const fs = require('fs');
const path = require('path');

async function testExtractor() {
  try {
    // Process a single file if provided as an argument
    if (process.argv.length > 2) {
      const filePath = process.argv[2];
      
      if (fs.existsSync(filePath)) {
        // First analyze the file
        analyzeWorksheet(filePath);
        
        // Then process it without saving to the database
        const result = await processExcelFile(filePath, {
          saveToDb: false
        });
        // Save the result to a JSON file for inspection
        const outputPath = `${path.basename(filePath, '.xlsx')}_extracted.json`;
        fs.writeFileSync(outputPath, JSON.stringify(result, null, 2));
      } else {
        console.error(`File not found: ${filePath}`);
      }
    } else {
    }
  } catch (error) {
    console.error(`An error occurred: ${error.message}`);
  }
}

// Run the test function
testExtractor().catch(console.error);
