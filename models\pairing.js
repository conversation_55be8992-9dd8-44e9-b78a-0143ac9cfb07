// models/pairing.js
const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class Pairing extends Model {
    static associate(models) {
      Pairing.belongsTo(models.Tournament, {
        foreignKey: "tournament_id",
      });
      Pairing.belongsTo(models.User, {
        foreignKey: "white_player_name",
        targetKey: "name",
        as: "whitePlayer",
        constraints: false,
      });

      Pairing.belongsTo(models.User, {
        foreignKey: "black_player_name",
        targetKey: "name",
        as: "blackPlayer",
        constraints: false,
      });
    }
  }
  Pairing.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      tournament_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      round_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      age_category: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      gender_category: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      board_no: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      white_player_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      white_player_ranking: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      white_player_fide_rating: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      white_player_points: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      black_player_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      black_player_ranking: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      black_player_fide_rating: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      black_player_points: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      result: {
        type: DataTypes.ENUM("white", "black", "draw"),
        allowNull: false,
      },
    },
    {
      sequelize,
      modelName: "Pairing",
      tableName: "pairings",
      timestamps: true,
    }
  );

  return Pairing;
};
