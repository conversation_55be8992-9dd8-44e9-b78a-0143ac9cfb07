'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create ENUM types first
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_refunds_refund_status" AS ENUM ('pending', 'processing', 'processed', 'failed');`
    );

    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_refunds_refund_type" AS ENUM ('full', 'partial');`
    );

    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_refunds_refund_reason" AS ENUM ('tournament_cancelled', 'player_cancelled', 'admin_cancelled', 'duplicate_payment', 'other');`
    );

    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_refunds_refund_speed" AS ENUM ('normal', 'optimum');`
    );

    await queryInterface.createTable('refunds', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        allowNull: false,
        primaryKey: true,
      },
      refund_reference: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
      },
      payment_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'payments',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      tournament_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'tournament',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      razorpay_refund_id: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: true,
      },
      razorpay_payment_id: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      refund_amount: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      refund_currency: {
        type: Sequelize.STRING(3),
        allowNull: false,
        defaultValue: 'INR',
      },
      refund_status: {
        type: "enum_refunds_refund_status",
        allowNull: false,
        defaultValue: 'pending',
      },
      refund_type: {
        type: "enum_refunds_refund_type",
        allowNull: false,
        defaultValue: 'full',
      },
      refund_reason: {
        type: "enum_refunds_refund_reason",
        allowNull: false,
        defaultValue: 'tournament_cancelled',
      },
      refund_remarks: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      refund_initiated_by: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      refund_speed: {
        type: "enum_refunds_refund_speed",
        allowNull: true,
        defaultValue: 'normal',
      },
      razorpay_response: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      refund_initiated_date: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('NOW'),
      },
      refund_completed_date: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('NOW'),
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('NOW'),
      },
    });

    // Create indexes as defined in the model
    await queryInterface.addIndex('refunds', ['refund_reference'], {
      unique: true,
      name: 'refunds_refund_reference_unique',
    });

    await queryInterface.addIndex('refunds', ['razorpay_refund_id'], {
      unique: true,
      name: 'refunds_razorpay_refund_id_unique',
    });

    await queryInterface.addIndex('refunds', ['razorpay_payment_id'], {
      name: 'refunds_razorpay_payment_id_index',
    });

    await queryInterface.addIndex('refunds', ['refund_status'], {
      name: 'refunds_refund_status_index',
    });

    await queryInterface.addIndex('refunds', ['payment_id'], {
      name: 'refunds_payment_id_index',
    });

    await queryInterface.addIndex('refunds', ['user_id', 'tournament_id'], {
      name: 'refunds_user_tournament_index',
    });

    await queryInterface.addIndex('refunds', ['refund_reason'], {
      name: 'refunds_refund_reason_index',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('refunds');
    
    // Drop ENUM types
    await queryInterface.sequelize.query(
      `DROP TYPE IF EXISTS "enum_refunds_refund_status";`
    );
    await queryInterface.sequelize.query(
      `DROP TYPE IF EXISTS "enum_refunds_refund_type";`
    );
    await queryInterface.sequelize.query(
      `DROP TYPE IF EXISTS "enum_refunds_refund_reason";`
    );
    await queryInterface.sequelize.query(
      `DROP TYPE IF EXISTS "enum_refunds_refund_speed";`
    );
  }
};