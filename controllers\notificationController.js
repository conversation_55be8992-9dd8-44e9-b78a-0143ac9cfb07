const { models } = require("../config/db");
const { sendResponse, handleError } = require("../utils/apiResponse");
const notificationService = require("../services/notificationService");
const cronService = require("../services/cronService");
const { z } = require("zod");

/**
 * Controller for notification-related endpoints
 */
const notificationController = {
  /**
   * Create pairing notifications for a tournament round
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createPairingNotifications: async (req, res) => {
    try {
      const { tournamentId, round } = req.body;
      const creatorId = req.user.userId;

      // Validation
      if (!tournamentId) {
        return sendResponse(res, 400, false, "Tournament ID is required");
      }

      if (!creatorId) {
        return sendResponse(res, 401, false, "Authentication required");
      }

      // Create notifications
      const notifications = await notificationService.createPairingNotifications(
        tournamentId,
        round,
        creatorId
      );

      // Count notifications by round
      const notificationsByRound = {};
      notifications.forEach((notification) => {
        const round = notification.metadata.round_id;
        notificationsByRound[round] = (notificationsByRound[round] || 0) + 1;
      });

      return sendResponse(res, 200, true, "Pairing notifications created", {
        totalNotifications: notifications.length,
        notificationsByRound
      });
    } catch (error) {
      return handleError(res, error);
    }
  },

  /**
   * Process pending SMS notifications immediately
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  processSmsNotifications: async (req, res) => {
    try {
      // Run the SMS notification processing job
      const result = await cronService.runJobNow("process-sms-notifications");

      return sendResponse(res, 200, true, "SMS notifications processed", {
        processed: result.total,
        success: result.success,
        failed: result.failed
      });
    } catch (error) {
      return handleError(res, error);
    }
  },

  /**
   * Get notification status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getNotificationStatus: async (req, res) => {
    try {
      const schema = z.object({
        type: z.enum(["tournament-pairing", "tournament-registration", "payment-confirmation"]).optional(),
        tournamentId: z.string().uuid().optional(),
        round: z.number().int().positive().optional(),
        status: z.enum(["pending", "retry", "delivered", "failed"]).optional(),
        limit: z.number().int().positive().max(100).default(20),
        offset: z.number().int().min(0).default(0)
      });

      const validationResult = schema.safeParse(req.query);
      if (!validationResult.success) {
        return sendResponse(res, 400, false, "Invalid query parameters", {
          errors: validationResult.error.errors
        });
      }

      const { type, tournamentId, round, status, limit, offset } = validationResult.data;

      // Build query
      const where = {};
      if (type) where.type = type;
      if (status) where.status = status;

      // Add tournament and round to metadata filter if provided
      if (tournamentId || round !== undefined) {
        where.metadata = {};
        if (tournamentId) where.metadata.tournament_id = tournamentId;
        if (round !== undefined) where.metadata.round_id = round;
      }

      // Get notifications
      const { Notifications } = models;
      const notifications = await Notifications.findAndCountAll({
        where,
        limit,
        offset,
        order: [["createdAt", "DESC"]],
        attributes: [
          "id",
          "userId",
          "type",
          "platform",
          "status",
          "deliveryAttempts",
          "maxAttempts",
          "sentAt",
          "lastAttemptAt",
          "nextAttemptAt",
          "failureReason",
          "metadata",
          "createdAt",
          "updatedAt"
        ]
      });

      return sendResponse(res, 200, true, "Notifications retrieved", {
        total: notifications.count,
        notifications: notifications.rows
      });
    } catch (error) {
      return handleError(res, error);
    }
  },

  /**
   * Create bulk registration notifications
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createBulkRegistrationNotifications: async (req, res) => {
    try {
      const { bulkRegistrationId } = req.body;
      const creatorId = req.user.userId;

      // Validation
      if (!bulkRegistrationId) {
        return sendResponse(res, 400, false, "Bulk Registration ID is required");
      }

      if (!creatorId) {
        return sendResponse(res, 401, false, "Authentication required");
      }

      // Create notifications
      const notifications = await notificationService.createBulkRegistrationNotifications(
        bulkRegistrationId,
        creatorId
      );

      // Count notifications by type
      const notificationsByType = {
        payment: 0,
        registration: 0
      };

      notifications.forEach((notification) => {
        if (notification.type === "payment-confirmation") {
          notificationsByType.payment++;
        } else if (notification.type === "tournament-registration") {
          notificationsByType.registration++;
        }
      });

      return sendResponse(res, 200, true, "Bulk registration notifications created", {
        totalNotifications: notifications.length,
        notificationsByType
      });
    } catch (error) {
      return handleError(res, error);
    }
  }
};

module.exports = notificationController;
