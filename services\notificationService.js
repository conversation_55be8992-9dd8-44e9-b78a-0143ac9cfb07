const { models } = require("../config/db");
const smsService = require("../utils/sms/smsService");

const { config } = require("../config/config");
const { Op } = require("sequelize");
const emailService = require("../utils/mailer/emailService");
const { sendEmail } = require("../utils/mailer");

/**
 * Service for processing notifications
 */
const notificationService = {
  /**
   * Process pending email notifications
   * @param {number} batchSize - Maximum number of notifications to process in one batch
   * @returns {Promise<Object>} - Processing results
   */
  processPendingPromotionalEmailNotifications: async (batchSize = 50) => {
    const { Notifications } = models;

    // Find pending email notifications
    const pendingNotifications = await Notifications.findPendingForProcessing(
      "email",
      "promotional",
      batchSize
    );

    console.log(
      `Processing ${pendingNotifications.length} pending email notifications`
    );

    const results = {
      total: pendingNotifications.length,
      success: 0,
      failed: 0,
      details: [],
    };

    // Process each notification
    for (const notification of pendingNotifications) {
      try {
        await processEmailNotification(notification);

        // Mark as sent
        await notification.markAsSent();
        results.success++;
        results.details.push({
          id: notification.id,
          status: "success",
          type: notification.type,
          email: notification.email,
        });
      } catch (error) {
        console.error(
          `Error processing email notification ${notification.id}:`,
          error
        );

        // Mark as failed with error message
        await notification.markAsFailed(error.message || "Unknown error");

        // Schedule retry if not permanently failed
        if (notification.status !== "failed") {
          await notification.scheduleNextAttempt();
        }

        results.failed++;
        results.details.push({
          id: notification.id,
          status: "failed",
          error: error.message,
          type: notification.type,
          email: notification.email,
        });
      }
    }

    return results;
  },

  /**
   * Process pending SMS notifications
   * @param {number} batchSize - Maximum number of notifications to process in one batch
   * @returns {Promise<Object>} - Processing results
   */
  processPendingSmsNotifications: async (batchSize = 50) => {
    const { Notifications } = models;

    // Find pending SMS notifications
    const pendingNotifications = await Notifications.findPendingForProcessing(
      "sms",
      "tournament-pairing",
      batchSize
    );

    console.log(
      `Processing ${pendingNotifications.length} pending SMS notifications`
    );

    const results = {
      total: pendingNotifications.length,
      success: 0,
      failed: 0,
      details: [],
    };

    // Process each notification
    for (const notification of pendingNotifications) {
      try {
        // Process based on notification type
        if (notification.type === "tournament-pairing") {
          await processPairingNotification(notification);
        } else if (notification.type === "tournament-registration") {
          await processRegistrationNotification(notification);
        } else if (notification.type === "payment-confirmation") {
          await processPaymentNotification(notification);
        } else if (notification.type === "promotional") {
          // Handle bulk SMS notifications
          await processBulkSmsNotification(notification);
        } else {
          // Skip unknown notification types
          console.warn(`Unknown notification type: ${notification.type}`);
          continue;
        }

        // Mark as sent
        await notification.markAsSent();
        results.success++;
        results.details.push({
          id: notification.id,
          status: "success",
          type: notification.type,
        });
      } catch (error) {
        console.error(
          `Error processing notification ${notification.id}:`,
          error
        );

        // Mark as failed with error message
        await notification.markAsFailed(error.message || "Unknown error");

        // Schedule retry if not permanently failed
        if (notification.status !== "failed") {
          await notification.scheduleNextAttempt();
        }

        results.failed++;
        results.details.push({
          id: notification.id,
          status: "failed",
          error: error.message,
          type: notification.type,
        });
      }
    }

    return results;
  },
 /**
   * Process pending email notifications
   * @param {string} type - Notification type
   * @param {string} template_id - Template ID
   * @param {number} batchSize - Maximum number of notifications to process in one batch
   * @returns {Promise<Object>} - Processing results
   */
  processPendingEmailNotifications: async (type,template_id,batchSize = 50) => {
    const { Notifications } = models;
   
    if(!type){
      throw new Error("type is missing");
    }

    // Find pending email notifications
    const pendingNotifications = await Notifications.findPendingForProcessing(
      "email",
      type,
      batchSize
    );

    console.log(
      `Processing ${pendingNotifications.length} pending email notifications`
    );

    const results = {
      total: pendingNotifications.length,
      success: 0,
      failed: 0,
      details: [],
    };

    // Process each notification
    for (const notification of pendingNotifications) {
      try {
       await sendEmail({
          to: notification.email,
          subject: notification.content.subject,
          templateName: notification.template_id || template_id,
          templateData: notification.content,
        });

        // Mark as sent
        await notification.markAsSent();
        results.success++;
        results.details.push({
          id: notification.id,
          status: "success",
          type: notification.type,
          email: notification.email,
        });
      } catch (error) {
        console.error(
          `Error processing email notification ${notification.id}:`,
          error
        );

        // Mark as failed with error message
        await notification.markAsFailed(error.message || "Unknown error");

        // Schedule retry if not permanently failed
        if (notification.status !== "failed") {
          await notification.scheduleNextAttempt();
        }

        results.failed++;
        results.details.push({
          id: notification.id,
          status: "failed",
          error: error.message,
          type: notification.type,
          email: notification.email,
        });
      }
    }

    return results;
  },

  /**
   * Create pairing notifications for a tournament round
   * @param {string} tournamentId - Tournament ID
   * @param {number} round - Round number
   * @param {string} creatorId - User ID of the creator
   * @returns {Promise<Array>} - Created notifications
   */
  createPairingNotifications: async (tournamentId, round, creatorId) => {
    const { Tournament, Pairing, User, Notifications } = models;

    // Get tournament info
    const tournament = await Tournament.findOne(
      { where: { id: tournamentId } },
      {
        attributes: ["id", "title", "city", "registrationStartDate"],
      }
    );

    if (!tournament) {
      throw new Error(`Tournament ${tournamentId} not found`);
    }

    // Build pairing query
    const pairingWhere = { tournament_id: tournament.id };

    // If round specified, only get pairings for that round
    if (round !== undefined) {
      pairingWhere.round_id = round;
    }

    // Get all pairings
    const pairings = await Pairing.findAll({
      where: pairingWhere,
      attributes: [
        "id",
        "round_id",
        "age_category",
        "gender_category",
        "white_player_name",
        "black_player_name",
        "board_no",
      ],
      order: [
        ["round_id", "ASC"],
        ["board_no", "ASC"],
      ],
    });

    if (!pairings.length) {
      throw new Error(
        `No pairings found for tournament ${tournamentId} round ${round}`
      );
    }

    // Get all player names from pairings
    const { Op } = require("sequelize");

    // Collect and trim all unique player names
    const playerNames = new Set();
    pairings.forEach((pairing) => {
      playerNames.add(pairing.white_player_name.trim());
      playerNames.add(pairing.black_player_name.trim());
    });

    // Build ILIKE conditions for each trimmed name
    const nameFilters = Array.from(playerNames).map((name) => ({
      name: { [Op.iLike]: name },
    }));

    // Query the User table
    const players = await User.findAll({
      where: {
        [Op.or]: nameFilters,
      },
      attributes: ["id", "name", "phoneNumber"],
    });

    // Create a map of player name to player details
    const playerMap = {};
    players.forEach((player) => {
      playerMap[player.name] = player;
    });

    // Create notifications for each player
    const notifications = [];
    const now = new Date();
    const expiresAt = new Date(now);
    expiresAt.setDate(expiresAt.getDate() + 7); // Expire in 7 days

    pairings.forEach((pairing) => {
      // Create notification for white player
      if (playerMap[pairing.white_player_name]) {
        const whitePlayer = playerMap[pairing.white_player_name];
        if (whitePlayer.phoneNumber) {
          notifications.push({
            userId: whitePlayer.id,
            creatorId: creatorId,
            phoneNumber: whitePlayer.phoneNumber,
            type: "tournament-pairing",
            platform: "sms",
            templateId: config.msg91.templates.pairing_notification_template_id,
            content: {
              roundno: pairing.round_id,
              boardno: `${pairing.board_no} | White`,
              opponent: pairing.black_player_name,
              color: "White",
              // moredetailslink: `${config.frontend_url}/tournaments/${tournament.title}/pairing-details/`,
            },
            status: "pending",
            priority: 2,
            expiresAt: expiresAt,
            metadata: {
              tournament_id: tournament.id,
              tournament_title: tournament.title,
              round_id: pairing.round_id,
              pairing_id: pairing.id,
              player_role: "white",
            },
          });
        }
      }

      // Create notification for black player
      if (playerMap[pairing.black_player_name]) {
        const blackPlayer = playerMap[pairing.black_player_name];
        if (blackPlayer.phoneNumber) {
          notifications.push({
            userId: blackPlayer.id,
            creatorId: creatorId,
            phoneNumber: blackPlayer.phoneNumber,
            type: "tournament-pairing",
            platform: "sms",
            templateId: config.msg91.templates.pairing_notification_template_id,
            content: {
              roundno: pairing.round_id,
              boardno: `${pairing.board_no} | Black`,
              opponent: pairing.white_player_name,
              color: "Black",
              // moredetailslink: `${config.frontend_url}/tournaments/${tournament.title}/pairing-details/`,
            },
            status: "pending",
            priority: 2,
            expiresAt: expiresAt,
            metadata: {
              tournament_id: tournament.id,
              tournament_title: tournament.title,
              round_id: pairing.round_id,
              pairing_id: pairing.id,
              player_role: "black",
            },
          });
        }
      }
    });

    // Save all notifications in bulk
    if (notifications.length > 0) {
      const chunkSize = 100;
      for (let i = 0; i < notifications.length; i += chunkSize) {
        await Notifications.bulkCreate(notifications.slice(i, i + chunkSize));
      }
    }

    return notifications;
  },

  /**
   * Create bulk registration notifications for a payment
   * @param {string} bulkRegistrationId - Bulk registration ID
   * @param {string} creatorId - User ID of the creator (club user)
   * @returns {Promise<Array>} - Created notifications
   */
  createBulkRegistrationNotifications: async (
    bulkRegistrationId,
    creatorId
  ) => {
    const { BulkRegistration, Tournament, User, Notifications, Payment } =
      models;

    // Get bulk registration info
    const bulkRegistration = await BulkRegistration.findOne({
      where: { id: bulkRegistrationId },
      include: [
        {
          model: Tournament,
          as: "tournament",
          attributes: ["id", "title", "startDate", "endDate"],
        },
        {
          model: Payment,
          as: "payment",
          attributes: ["id", "paymentTransactionId", "paymentAmount"],
        },
      ],
    });

    if (!bulkRegistration) {
      throw new Error(`Bulk registration ${bulkRegistrationId} not found`);
    }

    if (!bulkRegistration.tournament) {
      throw new Error(
        `Tournament not found for bulk registration ${bulkRegistrationId}`
      );
    }

    // Get club user who made the payment
    const clubUser = await User.findByPk(creatorId, {
      attributes: ["id", "name", "phoneNumber"],
    });

    if (!clubUser) {
      throw new Error(`Club user ${creatorId} not found`);
    }

    // Get all players in the bulk registration
    const playerIds = bulkRegistration.playerList.map(
      (player) => player.playerId
    );

    // Get player details
    const players = await User.findAll({
      where: { id: playerIds },
      attributes: ["id", "name", "phoneNumber"],
    });

    // Create notifications
    const notifications = [];
    const now = new Date();
    const expiresAt = new Date(now);
    expiresAt.setDate(expiresAt.getDate() + 7); // Expire in 7 days

    // Create payment confirmation notification for club
    if (clubUser.phoneNumber && bulkRegistration.payment) {
      notifications.push({
        userId: clubUser.id,
        creatorId: creatorId,
        phoneNumber: clubUser.phoneNumber,
        type: "payment-confirmation",
        platform: "sms",
        templateId: config.msg91.templates.payment_confirmation_template_id,
        content: {
          amount: bulkRegistration.payment.paymentAmount,
          tournament: bulkRegistration.tournament.title,
          transactionid: bulkRegistration.payment.paymentTransactionId,
        },
        status: "pending",
        priority: 2,
        expiresAt: expiresAt,
        metadata: {
          tournament_id: bulkRegistration.tournament.id,
          tournament_title: bulkRegistration.tournament.title,
          bulk_registration_id: bulkRegistration.id,
          payment_id: bulkRegistration.payment.id,
          user_type: "club",
        },
      });
    }

    // Create registration notifications for all players
    players.forEach((player) => {
      if (player.phoneNumber) {
        notifications.push({
          userId: player.id,
          creatorId: creatorId,
          phoneNumber: player.phoneNumber,
          type: "tournament-registration",
          platform: "sms",
          templateId:
            config.msg91.templates.tournament_registration_template_id,
          content: {
            tournament: bulkRegistration.tournament.title,
            startdate: bulkRegistration.tournament.startDate,
          },
          status: "pending",
          priority: 2,
          expiresAt: expiresAt,
          metadata: {
            tournament_id: bulkRegistration.tournament.id,
            tournament_title: bulkRegistration.tournament.title,
            bulk_registration_id: bulkRegistration.id,
            user_type: "player",
          },
        });
      }
    });

    // Save all notifications in bulk
    if (notifications.length > 0) {
      const chunkSize = 100;
      for (let i = 0; i < notifications.length; i += chunkSize) {
        await Notifications.bulkCreate(notifications.slice(i, i + chunkSize));
      }
    }

    return notifications;
  },
  processTournamentRegistrationOpenReminders: async () => {
    const platform = "email";
    const limit = 50;
    let total = 0,
      success = 0,
      failed = 0;
    const { Notifications } = models;

    // Find notifications that are ready to be sent (registration has opened)
    const pendingNotifications = await Notifications.findAll({
      where: {
        platform,
        type: "tournament-reminder",
        status: { [Op.in]: ["pending", "retry"] },
        expiresAt: { [Op.gt]: new Date() },
        nextAttemptAt: { [Op.lte]: new Date() },
        deliveryAttempts: { [Op.lt]: Sequelize.literal('"max_attempts"') },
      },
      order: [
        ["priority", "DESC"],
        ["createdAt", "ASC"],
      ],
      limit,
    });

    total = pendingNotifications.length;

    for (const notification of pendingNotifications) {
      try {
        // Send the email notification
        await sendEmail({
          to: notification.email,
          subject: `Registration Now Open: ${notification.content.tournamentName}`,
          templateName: notification.templateId || "tournament-reminder", // You'll need this template
          templateData: {
            recipientName: notification.content.recipientName,
            tournamentName: notification.content.tournamentName,
            registrationDeadline: notification.content.registrationDeadline,
            tournamentStartDate: notification.content.tournamentStartDate,
            tournamentEndDate: notification.content.tournamentEndDate,
            tournamentLocation: notification.content.tournamentLocation,
            registrationUrl: notification.content.registrationUrl,
            currentYear: new Date().getFullYear(),
          },
        });

        // Mark as sent
        await notification.markAsSent();
        success++;
      } catch (err) {
        await notification.markAsFailed(err.message);
        failed++;
      }
    }

    return { total, success, failed };
  },
  createCertificateNotifications: async (tournamentId, creatorId,tournamentTitle) => {
    const { User, Notifications, Certificate } = models;
    const { Op } = require("sequelize");

    // Build pairing query
    const certificateWhere = { tournament_id: tournamentId};

    // Get all pairings
    const certificates = await Certificate.findAll({
      where: certificateWhere,
      attributes: ["id", "player_name"],
    });

    if (!certificates.length) {
      throw new Error(`No certificate found for tournament`);
    }

    // Collect and trim all unique player names
    const playerNames = new Set();

    certificates.forEach((certificate) => {
      playerNames.add(certificate.player_name.trim());
    });

    // Build ILIKE conditions for each trimmed name
    const nameFilters = Array.from(playerNames).map((name) => ({
      name: { [Op.iLike]: name },
    }));

    // Query the User table
    const players = await User.findAll({
      where: {
        [Op.or]: nameFilters,
      },
      attributes: ["id", "name", "email"],
    });

    // Create a map of player name to player details
    const playerMap = {};
    players.forEach((player) => {
      playerMap[player.name] = player;
    });

    // Create notifications for each player
    const notifications = [];
    const now = new Date();
    const expiresAt = new Date(now);
    expiresAt.setDate(expiresAt.getDate() + 7); // Expire in 7 days

    certificates.forEach((certificate) => {
      // Create notification for white player
      if (playerMap[certificate.player_name]) {
        const player = playerMap[certificate.player_name];
        if (player.email) {
          notifications.push({
            userId: player.id,
            creatorId: creatorId,
            email: player.email,
            type: "certificate-generation",
            platform: "email",
            templateId: "tournament-certificate-generation",
            content: {
              name: certificate.player_name,
              subject: "Tournament Certificate Issued",
              email: certificate.email,
              title: tournamentTitle,
            },
            status: "pending",
            priority: 2,
            expiresAt: expiresAt,
            metadata: {
              tournament_id: tournamentId,
              tournament_title:tournamentTitle,
            },
          });
        }
      }
    });

    // Save all notifications in bulk
    if (notifications.length > 0) {
      const chunkSize = 100;
      for (let i = 0; i < notifications.length; i += chunkSize) {
        await Notifications.bulkCreate(notifications.slice(i, i + chunkSize));
      }
    }

    return notifications;
  },
};

/**
 * Process a pairing notification
 * @param {Object} notification - Notification object
 * @returns {Promise<void>}
 */
async function processPairingNotification(notification) {
  // Extract content from notification
  const { content, phoneNumber, templateId } = notification;

  if (!phoneNumber) {
    throw new Error("Phone number is missing");
  }

  if (!templateId) {
    throw new Error("Template ID is missing");
  }

  // Send SMS using the SMS service
  await smsService.sendCustomSMS({
    templateId,
    mobile: phoneNumber,
    variables: content,
  });
}

/**
 * Process a tournament registration notification
 * @param {Object} notification - Notification object
 * @returns {Promise<void>}
 */
async function processRegistrationNotification(notification) {
  // Extract content from notification
  const { content, phoneNumber, templateId } = notification;

  if (!phoneNumber) {
    throw new Error("Phone number is missing");
  }

  if (!templateId) {
    throw new Error("Template ID is missing");
  }

  // Send SMS using the SMS service
  await smsService.sendCustomSMS({
    templateId,
    mobile: phoneNumber,
    variables: content,
  });
}

/**
 * Process a payment confirmation notification
 * @param {Object} notification - Notification object
 * @returns {Promise<void>}
 */
async function processPaymentNotification(notification) {
  // Extract content from notification
  const { content, phoneNumber, templateId } = notification;

  if (!phoneNumber) {
    throw new Error("Phone number is missing");
  }

  if (!templateId) {
    throw new Error("Template ID is missing");
  }

  // Send SMS using the SMS service
  await smsService.sendCustomSMS({
    templateId,
    mobile: phoneNumber,
    variables: content,
  });
}

/**
 * Process an email notification
 * @param {Object} notification - Notification object
 * @returns {Promise<void>}
 */
async function processEmailNotification(notification) {
  // Extract content from notification
  const { content, email } = notification;

  if (!email) {
    throw new Error("Email is missing");
  }

  try {
    // All emails now use custom content approach
    if (content.customContent) {
      // Send custom content email using sendCustomEmail
      await emailService.sendCustomEmail({
        to: email,
        subject: content.subject,
        html: content.customContent,
      });
    } else if (content.html) {
      // Fallback for legacy notifications
      await emailService.sendCustomEmail({
        to: email,
        subject: content.subject,
        html: content.html,
      });
    } else {
      throw new Error("No valid email content found");
    }
  } catch (error) {
    console.error(`Failed to send email to ${email}:`, error);
    throw error;
  }
}


/**
 * Process a bulk SMS notification
 * @param {Object} notification - Notification object
 * @returns {Promise<void>}
 */
async function processBulkSmsNotification(notification) {
  // Extract content from notification
  const { content, phoneNumber, templateId } = notification;

  if (!phoneNumber) {
    throw new Error("Phone number is missing");
  }

  if (!templateId) {
    throw new Error("Template ID is missing");
  }

  // Send SMS using the SMS service
  await smsService.sendCustomSMS({
    templateId,
    mobile: phoneNumber,
    variables: content,
  });
}

module.exports = notificationService;
