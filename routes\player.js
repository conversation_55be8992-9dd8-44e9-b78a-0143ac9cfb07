const express = require("express");
const router = express.Router();
const profileRouter = express.Router();
const {
  createPlayerProfile,
  editPlayerProfile,
  getUserProfile,
  getAllPlayer,
  getSinglePlayer,
  getPlayerTournaments,
  getUpcomingTournament,
  uploadPlayerFiles,
  getPlayerDocuments,
  documentDownload,
  deletePlayerDocument,
  removeProfileImage,
  reportGenerate,
  createTournamentRegistrationReminder,
  getCertifcateDetails,
} = require("../controllers/playerController");
const verifyJwt = require("../middlewares/verifyJwt");
const { uploadFactory, handleUploadError } = require("../utils/s3");

const documentRouter = express.Router();

profileRouter.get("/", verifyJwt, getUserProfile);
profileRouter.post(
  "/",
  uploadFactory.player.profileAndDocuments(),
  handleUploadError,
  verifyJwt,
  createPlayerProfile
);
profileRouter.put(
  "/",
  verifyJwt,
  uploadFactory.player.profileImage(),
  handleUploadError,
  editPlayerProfile
);
profileRouter.get("/tournaments", verifyJwt, getUpcomingTournament);
profileRouter.delete("/remove-profile-image", verifyJwt, removeProfileImage);

router.get("/report", reportGenerate);
router.use("/profile", profileRouter);
router.use("/documents", documentRouter);
router.get("/", getAllPlayer);
router.get("/single/:id", getSinglePlayer);
router.get("/tournaments/:id", verifyJwt, getPlayerTournaments);
router.post(
  "/tournaments/:id/reminder",
  verifyJwt,
  createTournamentRegistrationReminder
);

documentRouter.post(
  "/",
  verifyJwt,
  uploadFactory.player.document(),
  handleUploadError,
  uploadPlayerFiles
);
documentRouter.get("/", verifyJwt, getPlayerDocuments);
documentRouter.delete("/:id", verifyJwt, deletePlayerDocument);
documentRouter.get("/:id/download-url", verifyJwt, documentDownload);
documentRouter.get("/certificates", verifyJwt, getCertifcateDetails);

module.exports = router;
