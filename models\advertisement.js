const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class Advertisement extends Model {
    static associate(models) {
      // Optional: If Advertisement belongs to a user or admin, you can define it here.
      // Advertisement.belongsTo(models.User, {
      //   foreignKey: "userId",
      //   onDelete: "CASCADE",
      //   as: "user"
      // });
    }
  }

  Advertisement.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      addId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        autoIncrement: true,
        unique: true,
      },
      userType: {
        type: DataTypes.ENUM("arbiter", "club", "player"),
        allowNull: false,
      },
      format: {
        type: DataTypes.ENUM("image", "gif", "video", "youtube"),
        allowNull: false,
      },
      url: {
        type: DataTypes.STRING(2083),
        allowNull: true,
        validate: {
          isUrl: true,
        },
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
      },
      country: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      state: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      city: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: "Advertisement",
      tableName: "advertisements",
      timestamps: true,
    }
  );

  return Advertisement;
};
