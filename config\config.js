const dotenv = require("dotenv");
// const path = require("path");
dotenv.config();

const config = {
  databaseUrl: `postgresql://${process.env.DB_USER}:${process.env.DB_PASSWORD}@${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_NAME}`,
  database: {
    host: process.env.DB_HOST || "localhost",
    port: process.env.DB_PORT || 5432,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    DBname: process.env.DB_NAME,
    dialect: "postgres",
    dialectOptions: process.env.NODE_ENV === "production" && {
      ssl: {
        require: true, // This will help you. But you will see nwe error
        rejectUnauthorized: false, // This line will fix new error
      },
    },
  },
  port: process.env.PORT ? parseInt(process.env.PORT, 10) : 3000,
  node_env: process.env.NODE_ENV || "development",
  aws: {
    region: process.env.AWS_ACCESS_REGION,
    access_key_id: process.env.AWS_ACCESS_KEY_ID,
    secret_access_key: process.env.AWS_SECRET_ACCESS_KEY,
    bucket_name: process.env.AWS_BUCKET_NAME || "chessbrigade-docs",
  },
  jwt_secret: process.env.JWT_SECRET, // Fixed typo from JWT_SECRECT
  razorpay: {
    key: process.env.RAZORPAY_KEY_ID,
    secret: process.env.RAZORPAY_KEY_SECRET,
    url_v1: process.env.RAZORPAY_URL_V1,
    url_v2: process.env.RAZORPAY_URL_V2,
    mid: process.env.RAZORPAY_MID,
    webhook_secret: process.env.RAZORPAY_WEBHOOK_SECRET,
    account_number: process.env.RAZORPAY_ACCOUNT_NUMBER,
  },
  frontend_url: process.env.FRONTEND_URL || "http://localhost:5173",
  backend_url: process.env.BACKEND_URL || "http://localhost:3000",
  email: {
    host: process.env.EMAIL_HOST || "smtp.gmail.com",
    port: process.env.EMAIL_PORT || 587,
    secure: process.env.EMAIL_SECURE === "true",
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
    from: process.env.EMAIL_FROM || "Chess Brigade <<EMAIL>>",
  },
  msg91: {
    authKey: process.env.MSG91_AUTH_KEY,
    senderId: process.env.MSG91_SENDER_ID || "JAINTL",
    baseUrl: "https://control.msg91.com/api/v5/flow/",
    templates: {
      otp_template_id: process.env.OTP_TEMPLATE_ID,
      tournament_registration_template_id:
        process.env.TOURNAMENT_REGISTRATION_TEMPLATE_ID,
      payment_confirmation_template_id:
        process.env.PAYMENT_CONFIRMATION_TEMPLATE_ID,
      tournament_withdrawal_template_id:
        process.env.TOURNAMENT_WITHDRAWAL_TEMPLATE_ID,
      pairing_notification_template_id:
        process.env.PAIRING_NOTIFICATION_TEMPLATE_ID,
        refund_confirmation_template_id:
        process.env.REFUND_CONFIRMATION_TEMPLATE_ID,
    },
  },
};

const database = config.database;
module.exports = { config, database };
