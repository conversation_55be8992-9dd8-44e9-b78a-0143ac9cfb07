const { S3Client, PutObjectCommand } = require("@aws-sdk/client-s3");
const { getSignedUrl } = require("@aws-sdk/s3-request-presigner");
const { config } = require("../config/config");

const presign = async (req, res) => {
  const { fileName: name, folderName: folder } = req.query;

  if (
    !name ||
    !folder ||
    typeof name !== "string" ||
    typeof folder !== "string"
  ) {
    return res.status(400).json({ error: "Invalid name or folder" });
  }

  const bucket = "car-rental-s3bucket";
  const contentType = "image/pdf";
  const key = `${folder}/${name}`;

  const s3Client = new S3Client({
    region: "eu-north-1",
    credentials: {
      accessKeyId: config.aws_access_key_id || "",
      secretAccessKey: config.aws_secret_access_key || "",
    },
  });

  try {
    const signedUrl = await getSignedUrlForUpload(
      s3Client,
      bucket,
      key,
      contentType
    );
    res.json({ signedUrl });
  } catch (error) {
    console.error("Error generating signed URL:", error);
    res.status(500).json({ error: "Failed to generate signed URL" });
  }
};

async function getSignedUrlForUpload(s3Client, bucket, key, contentType) {
  const command = new PutObjectCommand({
    Bucket: bucket,
    Key: key,
    ContentType: contentType,
  });

  try {
    // Generate a signed URL that expires in 15 minutes
    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: 900 });
    return signedUrl;
  } catch (err) {
    console.error("Oops! Something went wrong:", err);
    throw err;
  }
}

module.exports = { presign };
