"use strict";
const { Model, DataTypes } = require("sequelize");

module.exports = (sequelize) => {
  class Tournament extends Model {
    static associate(models) {
      Tournament.belongsTo(models.User, {
        foreignKey: "clubId",
        as: "club",
      });
      Tournament.hasMany(models.Registration, {
        foreignKey: "tournamentId",
        as: "registrations",
      });
      Tournament.hasMany(models.Payment, {
        foreignKey: "tournamentId",
        as: "payments",
      });
      Tournament.belongsTo(models.User, {
        foreignKey: "arbiterId",
        as: "arbiter",
      });
      Tournament.hasMany(models.Refund, {
        foreignKey: "tournamentId",
        as: "refunds",
      });
    }
  }

  Tournament.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      clubId: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "users",
          key: "id",
        },
        field: "club_id",
      },
      title: {
        type: DataTypes.STRING,
        unique: true,
        allowNull: false,
        validate: {
          notEmpty: true,
        },
      },  
      subTitle: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "sub_title",
      },
      presentedBy: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "presented_by",
      },
      fideRated: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
        field: "fide_rated",
      },
      organizerName: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
          notEmpty: true,
        },
        field: "organizer_name",
      },
      tournamentLevel: {
        type: DataTypes.ENUM("national", "state", "district", "global"),
        allowNull: false,
        validate: {
          isIn: [["national", "state", "district", "global"]],
        },
        defaultValue: "national",
        field: "tournament_level",
      },
      startDate: {
        type: DataTypes.DATEONLY,
        defaultValue: DataTypes.NOW,
        allowNull: false,
        field: "start_date",
      },
      endDate: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        field: "end_date",
      },
      reportingTime: {
        type: DataTypes.STRING,
        allowNull: false,
        field: "reporting_time",
      },
      registrationStartDate: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        field: "registration_start_date",
      },
      registrationEndDate: {
        type: DataTypes.DATEONLY,
        allowNull: false,
        field: "registration_end_date",
      },
      registrationEndTime: {
        type: DataTypes.STRING,
        allowNull: false,
        field: "registration_end_time",
      },
      arbiterId: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
          model: "users",
          key: "id",
        },
        field: "arbiter_id",
      },

      tournamentDirectorName: {
        type: DataTypes.STRING,
        allowNull: true,
        field: "tournament_director_name",
      },
      entryFeeCurrency: {
        type: DataTypes.ENUM("INR", "USD", "EUR"),
        defaultValue: "INR",
        allowNull: false,
        validate: {
          isIn: [["INR", "USD", "EUR"]],
        },
        field: "entry_fee_currency",
      },
      entryFee: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        field: "entry_fee",
      },

      numberOfRounds: {
        type: DataTypes.JSONB,
        defaultValue: { maleAgeCategory: {}, femaleAgeCategory: {} },
        allowNull: true,
        field: "number_of_rounds",
      },
      timeControl: {
        type: DataTypes.STRING(20),
        allowNull: false,
        field: "time_control",
      },
      timeControlDuration: {
        type: DataTypes.STRING(20),
        allowNull: false,
        field: "time_control_duration",
      },
      timeControlIncrement: {
        type: DataTypes.STRING(20),
        allowNull: false,
        field: "time_control_increment",
      },
      tournamentType: {
        type: DataTypes.ENUM("individual", "team"),
        defaultValue: "individual",
        allowNull: false,
        field: "tournament_type",
      },
      tournamentSystem: {
        type: DataTypes.ENUM("swiss-system", "round-robin", "knockout"),
        defaultValue: "swiss-system",
        allowNull: false,
        field: "tournament_system",
      },
      nationalApproval: {
        type: DataTypes.STRING,
        defaultValue: false,
        allowNull: true,
        field: "national_approval",
      },
      stateApproval: {
        type: DataTypes.STRING,
        defaultValue: false,
        allowNull: true,
        field: "state_approval",
      },
      districtApproval: {
        type: DataTypes.STRING,
        defaultValue: false,
        allowNull: true,
        field: "district_approval",
      },
      fideApproval: {
        type: DataTypes.STRING,
        defaultValue: false,
        allowNull: true,
        field: "fide_approval",
      },
      contactPersonName: {
        type: DataTypes.STRING,
        allowNull: false,
        field: "contact_person_name",
      },
      email: {
        type: DataTypes.STRING(100),
        allowNull: true,
       },

      contactNumber: {
        type: DataTypes.STRING(20),
        allowNull: false,
        field: "contact_number",
      },
      alternateContactNumber: {
        type: DataTypes.STRING(20),
        allowNull: true,
        field: "alternate_contact_number",
      },
      numberOfTrophiesMale: {
        type: DataTypes.SMALLINT,
        defaultValue: 0,
        allowNull: false,
        field: "number_of_trophies_male",
      },
      numberOfTrophiesFemale: {
        type: DataTypes.SMALLINT,
        defaultValue: 0,
        allowNull: false,
        field: "number_of_trophies_female",
      },
      totalCashPrizeCurrency: {
        type: DataTypes.ENUM("INR", "USD", "EUR"),
        defaultValue: "INR",
        allowNull: false,
        field: "total_cash_prize_currency",
      },
      totalCashPrizeAmount: {
        type: DataTypes.DECIMAL(10, 2),
        defaultValue: 0,
        allowNull: false,
        field: "total_cash_prize_amount",
      },
      country: {
        type: DataTypes.STRING(60),
        allowNull: true,
      },
      state: {
        type: DataTypes.STRING(60),
        allowNull: true,
      },
      district: {
        type: DataTypes.STRING(60),
        allowNull: true,
      },
      city: {
        type: DataTypes.STRING(60),
        allowNull: true,
      },
      pincode: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      venueAddress: {
        type: DataTypes.TEXT("tiny"),
        allowNull: true,
        field: "venue_address",
      },
      nearestLandmark: {
        type: DataTypes.STRING(100),
        allowNull: false,
        field: "nearest_landmark",
      },
      brochureUrl: {
        type: DataTypes.STRING(2083),
        allowNull: true,
        validate: {
          isUrl: true,
        },
        field: "brochure_url",
      },
      locationUrl: {
        type: DataTypes.STRING(2083),
        allowNull: true,
        validate: {
          isUrl: true,
        },
        field: "location_url",
      },
      chatUrl: {
        type: DataTypes.STRING(2083),
        allowNull: true,
        field: "chat_url",
      },
      chessboardProvided: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: true,
        field: "chessboard_provided",
      },
      timerProvided: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: true,
        field: "timer_provided",
      },
      parkingFacility: {
        type: DataTypes.ENUM("yes", "no", "limited"),
        defaultValue: "no",
        allowNull: true,
        field: "parking_facility",
      },
      spotEntry: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: true,
        field: "spot_entry",
      },
      tournamentStatus: {
        type: DataTypes.ENUM([
          "active",
          "inactive",
          "completed",
          "archived",
          "cancelled",
        ]),
        defaultValue: "inactive",
        allowNull: false,
        field: "tournament_status",
      },
      cancellationReason: {
        type: DataTypes.TEXT,
        allowNull: true,
        field: "cancellation_reason",
      },
      cancellationDate: {
        type: DataTypes.DATE,
        allowNull: true,
        field: "cancellation_date",
      },
      foodFacility: {
        type: DataTypes.STRING,
        allowNull: true,
        defaultValue: "nil",
        field: "food_facility",
        validate: {
          isValidFood(value) {
            const allowedValues = [
              "breakfast",
              "lunch",
              "dinner",
              "snacks",
              "beverages",
              "nil",
            ];
            const values = value.split(",");
            if (!values.every((val) => allowedValues.includes(val))) {
              throw new Error("Invalid food facility option.");
            }
          },
        },
        get() {
          const value = this.getDataValue("foodFacility");
          return value ? value.split(",") : [];
        },
        set(value) {
          if (Array.isArray(value)) {
            this.setDataValue("foodFacility", value.join(","));
          } else {
            this.setDataValue("foodFacility", value);
          }
        },
      },

      maleAgeCategory: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true,
        field: "male_age_category",
      },
      femaleAgeCategory: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true,
        field: "female_age_category",
      },
      tournamentCategory: {
        type: DataTypes.ENUM("open", "male", "female","children","children-open"),
        allowNull: true,
        defaultValue: "open",
        field: "tournament_category",
      },
      certificateData: {
        type: DataTypes.JSONB,
        allowNull: true,
        field: "certificate_data",
      },
    },
    {
      sequelize,
      modelName: "Tournament",
      tableName: "tournament",
      timestamps: true,
      indexes: [
        { fields: ["title"] },
        { fields: ["start_date"] },
        { fields: ["tournament_level"] },
        { fields: ["tournament_category"] },
        { fields: ["tournament_status"] },
      ],
    }
  );

  return Tournament;
};
