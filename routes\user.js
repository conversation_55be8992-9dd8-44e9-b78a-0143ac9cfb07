const router = require("express").Router();
const {
  getUserDetails,
  getClubInvite,
  updateClubInvite,
  leaveClub,
  SendclubInvite,
  getNotificationCount,
  sendFriendRequest,
  getFriendRequests,
  updateFriendRequest,
  removeFriend,
  checkFriendStatus,
  getAd,
  FindclubInvite,
  CancelclubInvite,
} = require("../controllers/userController");
const verifyJwt = require("../middlewares/verifyJwt");

// User profile routes
router.get("/", verifyJwt, getUserDetails);
router.get("/notification-count", verifyJwt, getNotificationCount);

// Club related routes
router.get("/club-invite", verifyJwt, getClubInvite);
router.post("/club-invite", verifyJwt, updateClubInvite);
router.post("/leave-club", verifyJwt, leaveClub);
router.post("/club-join-request", verifyJwt, SendclubInvite);
router.get("/get-club-request", verifyJwt, FindclubInvite);
router.post("/cancel-club-request", verifyJwt, CancelclubInvite);

// Friend request routes
router.post("/friend-request", verifyJwt, sendFriendRequest);
router.get("/friend-request", verifyJwt, getFriendRequests);
router.post("/friend-request/update", verifyJwt, updateFriendRequest);
router.post("/friend/remove", verifyJwt, removeFriend);
router.get("/check-friend-status/:id", verifyJwt, checkFriendStatus);

//user ad route
router.get('/ad/:userType',getAd)

module.exports = router;
