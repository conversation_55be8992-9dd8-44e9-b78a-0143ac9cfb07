'use strict';
const { Model, DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  class PlatformFee extends Model {
    static associate(models) {
      // Define associations here
    }

    // Get current active platform fee percentage
    static async getCurrentFeePercentage() {
      const activeFee = await this.findOne({
        where: { is_active: true },
        order: [['created_at', 'DESC']]
      });
      return activeFee?.fee_percentage || 3.75; // fallback to 3.75%
    }

    // Calculate fee amount based on base amount
    static async calculateFeeAmount(baseAmount) {
      const feePercentage = await this.getCurrentFeePercentage();
      return Math.round((baseAmount * feePercentage) / 100);
    }

    // Calculate total with platform fee
    static async calculateTotal(baseAmount) {
      const platformFee = await this.calculateFeeAmount(baseAmount);
      return baseAmount + platformFee;
    }

    // Update platform fee percentage
    static async updateFeePercentage(newFeePercentage) {
      const transaction = await sequelize.transaction();
      
      try {
        // Deactivate current active fee
        await this.update(
          { is_active: false },
          { where: { is_active: true }, transaction }
        );

        // Create new active fee
        const newFee = await this.create({
          fee_percentage: newFeePercentage,
          is_active: true
        }, { transaction });

        await transaction.commit();
        return newFee;
      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    }

    // Get fee breakdown for display
    static async getFeeBreakdown(baseAmount) {
      const feePercentage = await this.getCurrentFeePercentage();
      const feeAmount = await this.calculateFeeAmount(baseAmount);
      const total = baseAmount + feeAmount;

      return {
        baseAmount,
        feePercentage,
        feeAmount,
        total
      };
    }
  }

  PlatformFee.init({
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    fee_percentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 3.75,
      validate: {
        min: 0,
        max: 100
      }
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
  }, {
    sequelize,
    modelName: 'PlatformFee',
    tableName: 'platform_fees',
    underscored: true,
    timestamps: true,
    indexes: [
      { fields: ['is_active'] },
      { fields: ['fee_percentage'] }
    ]
  });

  return PlatformFee;
};